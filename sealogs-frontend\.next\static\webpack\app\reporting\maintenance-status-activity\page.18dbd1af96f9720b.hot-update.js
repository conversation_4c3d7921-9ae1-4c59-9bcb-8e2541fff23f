"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/reporting/maintenance-status-activity/page",{

/***/ "(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx":
/*!*********************************************************************!*\
  !*** ./src/app/ui/reporting/maintenance-status-activity-report.tsx ***!
  \*********************************************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": function() { return /* binding */ MaintenanceStatusActivityReport; },\n/* harmony export */   dueStatusLabel: function() { return /* binding */ dueStatusLabel; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES */ \"(app-pages-browser)/./src/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES.ts\");\n/* harmony import */ var _apollo_client__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @apollo/client */ \"(app-pages-browser)/./node_modules/.pnpm/@apollo+client@3.13.8_@type_3a2c516023e671829487810ec4a9ea8f/node_modules/@apollo/client/react/hooks/useLazyQuery.js\");\n/* harmony import */ var _app_lib_actions__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/app/lib/actions */ \"(app-pages-browser)/./src/app/lib/actions.tsx\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! dayjs */ \"(app-pages-browser)/./node_modules/.pnpm/dayjs@1.11.13/node_modules/dayjs/dayjs.min.js\");\n/* harmony import */ var dayjs__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(dayjs__WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! lodash/isEmpty */ \"(app-pages-browser)/./node_modules/.pnpm/lodash@4.17.21/node_modules/lodash/isEmpty.js\");\n/* harmony import */ var lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/app/helpers/csvHelper */ \"(app-pages-browser)/./src/app/helpers/csvHelper.ts\");\n/* harmony import */ var _app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/app/helpers/pdfHelper */ \"(app-pages-browser)/./src/app/helpers/pdfHelper.ts\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/.pnpm/next@14.2.30_@babel+core@7._a6136f869fa4cd4c2825fdf5844c1468/node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_filteredTable__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/filteredTable */ \"(app-pages-browser)/./src/components/filteredTable.tsx\");\n/* harmony import */ var _components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/data-table-sort-header */ \"(app-pages-browser)/./src/components/data-table-sort-header.tsx\");\n/* harmony import */ var _app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/app/ui/maintenance/list/list */ \"(app-pages-browser)/./src/app/ui/maintenance/list/list.tsx\");\n/* harmony import */ var _components_ui__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/components/ui */ \"(app-pages-browser)/./src/components/ui/index.ts\");\n/* harmony import */ var _components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/components/filter/components/maintenance-report-actions */ \"(app-pages-browser)/./src/components/filter/components/maintenance-report-actions.tsx\");\n/* __next_internal_client_entry_do_not_use__ default,dueStatusLabel auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n// Helper function to extract status text using the exact same logic as StatusBadge\n// This ensures consistency between visual display and exported data\nconst getStatusText = (isOverDue)=>{\n    let statusText = \"\";\n    if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(isOverDue.status)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) === \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Upcoming\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days)) {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status;\n    } else if ((isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) && (isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days) !== \"Save As Draft\") {\n        statusText = isOverDue === null || isOverDue === void 0 ? void 0 : isOverDue.days;\n    }\n    return statusText || \"\";\n};\n// Helper function to create a compatible MaintenanceCheck object for StatusBadge\nconst createMaintenanceCheckForBadge = (reportItem)=>{\n    return {\n        id: 0,\n        assignedTo: {\n            id: 0,\n            name: \"\"\n        },\n        basicComponent: {\n            id: 0,\n            title: null\n        },\n        inventory: {\n            id: 0,\n            item: null\n        },\n        status: reportItem.status || \"\",\n        recurringID: 0,\n        name: reportItem.taskName,\n        created: \"\",\n        severity: \"\",\n        isOverDue: reportItem.dueStatus,\n        comments: null,\n        workOrderNumber: null,\n        startDate: \"\",\n        expires: null,\n        maintenanceCategoryID: 0\n    };\n};\n// Column definitions for the DataTable\nconst columns = (0,_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.createColumns)([\n    {\n        accessorKey: \"taskName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Task Name\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 101,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"font-medium\",\n                children: item.taskName\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 105,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"inventoryName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Inventory\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 111,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.inventoryName || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 115,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"vesselName\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Location\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 121,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.vesselName || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 125,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"assignedTo\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Assigned To\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 131,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.assignedTo || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 135,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"status\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 141,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.status || \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 145,\n                columnNumber: 20\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"dueDate\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Due Date\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 151,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YY\") : \"\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 156,\n                columnNumber: 17\n            }, undefined);\n        }\n    },\n    {\n        accessorKey: \"dueStatus\",\n        header: (param)=>{\n            let { column } = param;\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_data_table_sort_header__WEBPACK_IMPORTED_MODULE_10__.DataTableSortHeader, {\n                column: column,\n                title: \"Due Status\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 165,\n                columnNumber: 13\n            }, undefined);\n        },\n        cell: (param)=>{\n            let { row } = param;\n            const item = row.original;\n            const maintenanceCheck = createMaintenanceCheckForBadge(item);\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_app_ui_maintenance_list_list__WEBPACK_IMPORTED_MODULE_11__.StatusBadge, {\n                maintenanceCheck: maintenanceCheck\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 170,\n                columnNumber: 20\n            }, undefined);\n        }\n    }\n]);\n// Function to evaluate row status for highlighting\nconst getRowStatus = (rowData)=>{\n    var _rowData_dueStatus, _rowData_dueStatus1, _rowData_dueStatus2;\n    if (((_rowData_dueStatus = rowData.dueStatus) === null || _rowData_dueStatus === void 0 ? void 0 : _rowData_dueStatus.status) === \"High\") {\n        return \"overdue\";\n    }\n    if (((_rowData_dueStatus1 = rowData.dueStatus) === null || _rowData_dueStatus1 === void 0 ? void 0 : _rowData_dueStatus1.status) === \"Medium\" || ((_rowData_dueStatus2 = rowData.dueStatus) === null || _rowData_dueStatus2 === void 0 ? void 0 : _rowData_dueStatus2.status) === \"Low\") {\n        return \"upcoming\";\n    }\n    return \"normal\";\n};\nfunction MaintenanceStatusActivityReport() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter)();\n    const [selectedVessels, setSelectedVessels] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [category, setCategory] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [crew, setCrew] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [dateRange, setDateRange] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        startDate: new Date(),\n        endDate: new Date()\n    });\n    const [getReportData, { called, loading, data }] = (0,_apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery)(_app_lib_graphQL_query_reporting_GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES__WEBPACK_IMPORTED_MODULE_2__.GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES, {\n        fetchPolicy: \"cache-and-network\",\n        onError: (error)=>{\n            console.error(\"GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error\", error);\n        }\n    });\n    const handleFilterOnChange = (param)=>{\n        let { type, data } = param;\n        switch(type){\n            case \"vessels\":\n                setSelectedVessels(data);\n                break;\n            case \"category\":\n                setCategory(data);\n                break;\n            case \"status\":\n                setStatus(data);\n                break;\n            case \"dateRange\":\n                setDateRange(data || {\n                    startDate: null,\n                    endDate: null\n                });\n                break;\n            case \"member\":\n                setCrew(data);\n                break;\n            default:\n                break;\n        }\n    };\n    const generateReport = ()=>{\n        const filter = {};\n        if (dateRange && dateRange.startDate !== null && dateRange.endDate !== null) {\n            // Format dates as YYYY-MM-DD strings for GraphQL\n            const startDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.startDate).format(\"YYYY-MM-DD\");\n            const endDateFormatted = dayjs__WEBPACK_IMPORTED_MODULE_4___default()(dateRange.endDate).format(\"YYYY-MM-DD\");\n            filter[\"expires\"] = {\n                gte: startDateFormatted,\n                lte: endDateFormatted\n            };\n        }\n        if (selectedVessels.length > 0) {\n            filter[\"basicComponentID\"] = {\n                in: selectedVessels.map((item)=>item.value)\n            };\n        }\n        if (category !== null) {\n            filter[\"maintenanceCategoryID\"] = {\n                eq: category.value\n            };\n        }\n        if (status !== null) {\n            filter[\"status\"] = {\n                eq: status.value\n            };\n        }\n        if (crew !== null) {\n            filter[\"assignedToID\"] = {\n                eq: crew.value\n            };\n        }\n        getReportData({\n            variables: {\n                filter\n            }\n        });\n    };\n    const downloadPdf = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const body = reportData.map((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            return [\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"\",\n                getStatusText(item.dueStatus)\n            ];\n        });\n        const headers = [\n            [\n                \"Task Name\",\n                \"Inventory\",\n                \"Location\",\n                \"Assigned To\",\n                \"Status\",\n                \"Due Date\",\n                \"Due Status\"\n            ]\n        ];\n        (0,_app_helpers_pdfHelper__WEBPACK_IMPORTED_MODULE_7__.exportPdfTable)({\n            body,\n            headers\n        });\n    };\n    const downloadCsv = ()=>{\n        if (reportData.length === 0) {\n            return;\n        }\n        const csvEntries = [\n            [\n                \"task name\",\n                \"inventory\",\n                \"location\",\n                \"assigned to\",\n                \"status\",\n                \"due date\",\n                \"due status\"\n            ]\n        ];\n        reportData.forEach((item)=>{\n            var _item_inventoryName, _item_vesselName, _item_assignedTo, _item_status;\n            csvEntries.push([\n                item.taskName,\n                (_item_inventoryName = item.inventoryName) !== null && _item_inventoryName !== void 0 ? _item_inventoryName : \"N/A\",\n                (_item_vesselName = item.vesselName) !== null && _item_vesselName !== void 0 ? _item_vesselName : \"N/A\",\n                (_item_assignedTo = item.assignedTo) !== null && _item_assignedTo !== void 0 ? _item_assignedTo : \"N/A\",\n                (_item_status = item.status) !== null && _item_status !== void 0 ? _item_status : \"N/A\",\n                item.dueDate ? dayjs__WEBPACK_IMPORTED_MODULE_4___default()(item.dueDate).format(\"DD/MM/YYYY\") : \"N/A\",\n                getStatusText(item.dueStatus)\n            ]);\n        });\n        (0,_app_helpers_csvHelper__WEBPACK_IMPORTED_MODULE_6__.exportCsv)(csvEntries);\n    };\n    const reportData = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        var _data_readComponentMaintenanceChecks_nodes;\n        const fetchedData = (_data_readComponentMaintenanceChecks_nodes = data === null || data === void 0 ? void 0 : data.readComponentMaintenanceChecks.nodes) !== null && _data_readComponentMaintenanceChecks_nodes !== void 0 ? _data_readComponentMaintenanceChecks_nodes : [];\n        if (fetchedData.length === 0) {\n            return [];\n        }\n        const reportItems = [];\n        fetchedData.forEach((fetchedItem)=>{\n            const reportItem = {\n                taskName: fetchedItem.name,\n                vesselName: fetchedItem.basicComponent.title,\n                assignedTo: fetchedItem.assignedTo.id == 0 ? undefined : \"\".concat(fetchedItem.assignedTo.firstName, \" \").concat(fetchedItem.assignedTo.surname),\n                inventoryName: fetchedItem.inventory.title,\n                dueDate: fetchedItem.expires ? new Date(fetchedItem.expires) : undefined,\n                status: fetchedItem.status,\n                dueStatus: (0,_app_lib_actions__WEBPACK_IMPORTED_MODULE_3__.isOverDueTask)(fetchedItem)\n            };\n            reportItems.push(reportItem);\n        });\n        return reportItems;\n    }, [\n        called,\n        loading,\n        data\n    ]);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui__WEBPACK_IMPORTED_MODULE_12__.ListHeader, {\n                title: \"Maintenance status and activity report\",\n                actions: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filter_components_maintenance_report_actions__WEBPACK_IMPORTED_MODULE_13__.MaintenanceReportFilterActions, {\n                    onDownloadCsv: downloadCsv,\n                    onDownloadPdf: downloadPdf\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                    lineNumber: 394,\n                    columnNumber: 21\n                }, void 0)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 391,\n                columnNumber: 13\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_filteredTable__WEBPACK_IMPORTED_MODULE_9__.DataTable, {\n                columns: columns,\n                data: reportData,\n                isLoading: called && loading,\n                rowStatus: getRowStatus,\n                onChange: handleFilterOnChange,\n                onFilterClick: generateReport,\n                showToolbar: true\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Music\\\\SeaLogsV2\\\\sealogs-frontend\\\\src\\\\app\\\\ui\\\\reporting\\\\maintenance-status-activity-report.tsx\",\n                lineNumber: 401,\n                columnNumber: 13\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(MaintenanceStatusActivityReport, \"oU/hWC7Aj40C5AHa8r/sGIGstSo=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_8__.useRouter,\n        _apollo_client__WEBPACK_IMPORTED_MODULE_14__.useLazyQuery\n    ];\n});\n_c = MaintenanceStatusActivityReport;\nconst dueStatusLabel = (dueStatus)=>{\n    return \"\".concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) && [\n        \"High\",\n        \"Medium\",\n        \"Low\"\n    ].includes(dueStatus.status) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) === \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Upcoming\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status : \"\").concat((dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.status) === \"Completed\" && !lodash_isEmpty__WEBPACK_IMPORTED_MODULE_5___default()(dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) && (dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days) !== \"Save As Draft\" ? dueStatus === null || dueStatus === void 0 ? void 0 : dueStatus.days : \"\");\n};\nvar _c;\n$RefreshReg$(_c, \"MaintenanceStatusActivityReport\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9hcHAvdWkvcmVwb3J0aW5nL21haW50ZW5hbmNlLXN0YXR1cy1hY3Rpdml0eS1yZXBvcnQudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFFZ0Q7QUFFbUY7QUFDdEY7QUFDSTtBQUN4QjtBQUNPO0FBQ21CO0FBQ0s7QUFDYjtBQUNxQztBQUNQO0FBQ2I7QUFHVztBQUNtQztBQTRCMUcsbUZBQW1GO0FBQ25GLG9FQUFvRTtBQUNwRSxNQUFNaUIsZ0JBQWdCLENBQUNDO0lBQ25CLElBQUlDLGFBQWE7SUFDakIsSUFDSUQsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXRSxNQUFNLEtBQ2pCO1FBQUM7UUFBUTtRQUFVO0tBQU0sQ0FBQ0MsUUFBUSxDQUFDSCxVQUFVRSxNQUFNLEdBQ3JEO1FBQ0VELGFBQWFELHNCQUFBQSxnQ0FBQUEsVUFBV0ksSUFBSTtJQUNoQyxPQUFPLElBQ0hKLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV0UsTUFBTSxNQUFLLGVBQ3RCRixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdJLElBQUksTUFBSyxpQkFDdEI7UUFDRUgsYUFBYUQsc0JBQUFBLGdDQUFBQSxVQUFXSSxJQUFJO0lBQ2hDLE9BQU8sSUFBSUosQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXRSxNQUFNLE1BQUssWUFBWTtRQUN6Q0QsYUFBYUQsc0JBQUFBLGdDQUFBQSxVQUFXSSxJQUFJO0lBQ2hDLE9BQU8sSUFBSUosQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXRSxNQUFNLE1BQUssZUFBZWIscURBQU9BLENBQUNXLHNCQUFBQSxnQ0FBQUEsVUFBV0ksSUFBSSxHQUFHO1FBQ3RFSCxhQUFhRCxzQkFBQUEsZ0NBQUFBLFVBQVdFLE1BQU07SUFDbEMsT0FBTyxJQUNIRixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdFLE1BQU0sTUFBSyxlQUN0QixDQUFDYixxREFBT0EsQ0FBQ1csc0JBQUFBLGdDQUFBQSxVQUFXSSxJQUFJLEtBQ3hCSixDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdJLElBQUksTUFBSyxpQkFDdEI7UUFDRUgsYUFBYUQsc0JBQUFBLGdDQUFBQSxVQUFXSSxJQUFJO0lBQ2hDO0lBQ0EsT0FBT0gsY0FBYztBQUN6QjtBQUVBLGlGQUFpRjtBQUNqRixNQUFNSSxpQ0FBaUMsQ0FBQ0M7SUFDcEMsT0FBTztRQUNIQyxJQUFJO1FBQ0pDLFlBQVk7WUFBRUQsSUFBSTtZQUFHRSxNQUFNO1FBQUc7UUFDOUJDLGdCQUFnQjtZQUFFSCxJQUFJO1lBQUdJLE9BQU87UUFBSztRQUNyQ0MsV0FBVztZQUFFTCxJQUFJO1lBQUdNLE1BQU07UUFBSztRQUMvQlgsUUFBUUksV0FBV0osTUFBTSxJQUFJO1FBQzdCWSxhQUFhO1FBQ2JMLE1BQU1ILFdBQVdTLFFBQVE7UUFDekJDLFNBQVM7UUFDVEMsVUFBVTtRQUNWakIsV0FBV00sV0FBV1ksU0FBUztRQUMvQkMsVUFBVTtRQUNWQyxpQkFBaUI7UUFDakJDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyx1QkFBdUI7SUFDM0I7QUFDSjtBQUVBLHVDQUF1QztBQUN2QyxNQUFNQyxVQUFVL0Isd0VBQWFBLENBQWM7SUFDdkM7UUFDSWdDLGFBQWE7UUFDYkMsUUFBUTtnQkFBQyxFQUFFQyxNQUFNLEVBQW1CO2lDQUNoQyw4REFBQ2hDLG9GQUFtQkE7Z0JBQUNnQyxRQUFRQTtnQkFBUWhCLE9BQU07Ozs7Ozs7UUFFL0NpQixNQUFNO2dCQUFDLEVBQUVDLEdBQUcsRUFBZ0I7WUFDeEIsTUFBTWhCLE9BQU9nQixJQUFJQyxRQUFRO1lBQ3pCLHFCQUFPLDhEQUFDQztnQkFBSUMsV0FBVTswQkFBZW5CLEtBQUtFLFFBQVE7Ozs7OztRQUN0RDtJQUNKO0lBQ0E7UUFDSVUsYUFBYTtRQUNiQyxRQUFRO2dCQUFDLEVBQUVDLE1BQU0sRUFBbUI7aUNBQ2hDLDhEQUFDaEMsb0ZBQW1CQTtnQkFBQ2dDLFFBQVFBO2dCQUFRaEIsT0FBTTs7Ozs7OztRQUUvQ2lCLE1BQU07Z0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtZQUN4QixNQUFNaEIsT0FBT2dCLElBQUlDLFFBQVE7WUFDekIscUJBQU8sOERBQUNDOzBCQUFLbEIsS0FBS29CLGFBQWEsSUFBSTs7Ozs7O1FBQ3ZDO0lBQ0o7SUFDQTtRQUNJUixhQUFhO1FBQ2JDLFFBQVE7Z0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtpQ0FDaEMsOERBQUNoQyxvRkFBbUJBO2dCQUFDZ0MsUUFBUUE7Z0JBQVFoQixPQUFNOzs7Ozs7O1FBRS9DaUIsTUFBTTtnQkFBQyxFQUFFQyxHQUFHLEVBQWdCO1lBQ3hCLE1BQU1oQixPQUFPZ0IsSUFBSUMsUUFBUTtZQUN6QixxQkFBTyw4REFBQ0M7MEJBQUtsQixLQUFLcUIsVUFBVSxJQUFJOzs7Ozs7UUFDcEM7SUFDSjtJQUNBO1FBQ0lULGFBQWE7UUFDYkMsUUFBUTtnQkFBQyxFQUFFQyxNQUFNLEVBQW1CO2lDQUNoQyw4REFBQ2hDLG9GQUFtQkE7Z0JBQUNnQyxRQUFRQTtnQkFBUWhCLE9BQU07Ozs7Ozs7UUFFL0NpQixNQUFNO2dCQUFDLEVBQUVDLEdBQUcsRUFBZ0I7WUFDeEIsTUFBTWhCLE9BQU9nQixJQUFJQyxRQUFRO1lBQ3pCLHFCQUFPLDhEQUFDQzswQkFBS2xCLEtBQUtMLFVBQVUsSUFBSTs7Ozs7O1FBQ3BDO0lBQ0o7SUFDQTtRQUNJaUIsYUFBYTtRQUNiQyxRQUFRO2dCQUFDLEVBQUVDLE1BQU0sRUFBbUI7aUNBQ2hDLDhEQUFDaEMsb0ZBQW1CQTtnQkFBQ2dDLFFBQVFBO2dCQUFRaEIsT0FBTTs7Ozs7OztRQUUvQ2lCLE1BQU07Z0JBQUMsRUFBRUMsR0FBRyxFQUFnQjtZQUN4QixNQUFNaEIsT0FBT2dCLElBQUlDLFFBQVE7WUFDekIscUJBQU8sOERBQUNDOzBCQUFLbEIsS0FBS1gsTUFBTSxJQUFJOzs7Ozs7UUFDaEM7SUFDSjtJQUNBO1FBQ0l1QixhQUFhO1FBQ2JDLFFBQVE7Z0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtpQ0FDaEMsOERBQUNoQyxvRkFBbUJBO2dCQUFDZ0MsUUFBUUE7Z0JBQVFoQixPQUFNOzs7Ozs7O1FBRS9DaUIsTUFBTTtnQkFBQyxFQUFFQyxHQUFHLEVBQWdCO1lBQ3hCLE1BQU1oQixPQUFPZ0IsSUFBSUMsUUFBUTtZQUN6QixxQkFDSSw4REFBQ0M7MEJBQ0lsQixLQUFLc0IsT0FBTyxHQUFHL0MsNENBQUtBLENBQUN5QixLQUFLc0IsT0FBTyxFQUFFQyxNQUFNLENBQUMsY0FBYzs7Ozs7O1FBR3JFO0lBQ0o7SUFDQTtRQUNJWCxhQUFhO1FBQ2JDLFFBQVE7Z0JBQUMsRUFBRUMsTUFBTSxFQUFtQjtpQ0FDaEMsOERBQUNoQyxvRkFBbUJBO2dCQUFDZ0MsUUFBUUE7Z0JBQVFoQixPQUFNOzs7Ozs7O1FBRS9DaUIsTUFBTTtnQkFBQyxFQUFFQyxHQUFHLEVBQWdCO1lBQ3hCLE1BQU1oQixPQUFPZ0IsSUFBSUMsUUFBUTtZQUN6QixNQUFNTyxtQkFBbUJoQywrQkFBK0JRO1lBQ3hELHFCQUFPLDhEQUFDakIsdUVBQVdBO2dCQUFDeUMsa0JBQWtCQTs7Ozs7O1FBQzFDO0lBQ0o7Q0FDSDtBQUVELG1EQUFtRDtBQUNuRCxNQUFNQyxlQUFlLENBQUNDO1FBQ2RBLG9CQUlBQSxxQkFDQUE7SUFMSixJQUFJQSxFQUFBQSxxQkFBQUEsUUFBUXJCLFNBQVMsY0FBakJxQix5Q0FBQUEsbUJBQW1CckMsTUFBTSxNQUFLLFFBQVE7UUFDdEMsT0FBTztJQUNYO0lBQ0EsSUFDSXFDLEVBQUFBLHNCQUFBQSxRQUFRckIsU0FBUyxjQUFqQnFCLDBDQUFBQSxvQkFBbUJyQyxNQUFNLE1BQUssWUFDOUJxQyxFQUFBQSxzQkFBQUEsUUFBUXJCLFNBQVMsY0FBakJxQiwwQ0FBQUEsb0JBQW1CckMsTUFBTSxNQUFLLE9BQ2hDO1FBQ0UsT0FBTztJQUNYO0lBQ0EsT0FBTztBQUNYO0FBRWUsU0FBU3NDOztJQUNwQixNQUFNQyxTQUFTakQsMERBQVNBO0lBQ3hCLE1BQU0sQ0FBQ2tELGlCQUFpQkMsbUJBQW1CLEdBQUczRCwrQ0FBUUEsQ0FBa0IsRUFBRTtJQUMxRSxNQUFNLENBQUM0RCxVQUFVQyxZQUFZLEdBQUc3RCwrQ0FBUUEsQ0FBdUI7SUFDL0QsTUFBTSxDQUFDa0IsUUFBUTRDLFVBQVUsR0FBRzlELCtDQUFRQSxDQUF1QjtJQUMzRCxNQUFNLENBQUMrRCxNQUFNQyxRQUFRLEdBQUdoRSwrQ0FBUUEsQ0FBdUI7SUFDdkQsTUFBTSxDQUFDaUUsV0FBV0MsYUFBYSxHQUFHbEUsK0NBQVFBLENBQVk7UUFDbERxQyxXQUFXLElBQUk4QjtRQUNmQyxTQUFTLElBQUlEO0lBQ2pCO0lBRUEsTUFBTSxDQUFDRSxlQUFlLEVBQUVDLE1BQU0sRUFBRUMsT0FBTyxFQUFFQyxJQUFJLEVBQUUsQ0FBQyxHQUFHdEUsNkRBQVlBLENBQzNERCw2SUFBdUNBLEVBQ3ZDO1FBQ0l3RSxhQUFhO1FBQ2JDLFNBQVMsQ0FBQ0M7WUFDTkMsUUFBUUQsS0FBSyxDQUNULGlEQUNBQTtRQUVSO0lBQ0o7SUFHSixNQUFNRSx1QkFBdUI7WUFBQyxFQUFFQyxJQUFJLEVBQUVOLElBQUksRUFBVztRQUNqRCxPQUFRTTtZQUNKLEtBQUs7Z0JBQ0RuQixtQkFBbUJhO2dCQUNuQjtZQUNKLEtBQUs7Z0JBQ0RYLFlBQVlXO2dCQUNaO1lBRUosS0FBSztnQkFDRFYsVUFBVVU7Z0JBQ1Y7WUFFSixLQUFLO2dCQUNETixhQUFhTSxRQUFRO29CQUFFbkMsV0FBVztvQkFBTStCLFNBQVM7Z0JBQUs7Z0JBQ3REO1lBRUosS0FBSztnQkFDREosUUFBUVE7Z0JBQ1I7WUFFSjtnQkFDSTtRQUNSO0lBQ0o7SUFFQSxNQUFNTyxpQkFBaUI7UUFDbkIsTUFBTUMsU0FBYyxDQUFDO1FBRXJCLElBQ0lmLGFBQ0FBLFVBQVU1QixTQUFTLEtBQUssUUFDeEI0QixVQUFVRyxPQUFPLEtBQUssTUFDeEI7WUFDRSxpREFBaUQ7WUFDakQsTUFBTWEscUJBQXFCN0UsNENBQUtBLENBQUM2RCxVQUFVNUIsU0FBUyxFQUFFZSxNQUFNLENBQ3hEO1lBRUosTUFBTThCLG1CQUFtQjlFLDRDQUFLQSxDQUFDNkQsVUFBVUcsT0FBTyxFQUFFaEIsTUFBTSxDQUNwRDtZQUdKNEIsTUFBTSxDQUFDLFVBQVUsR0FBRztnQkFDaEJHLEtBQUtGO2dCQUNMRyxLQUFLRjtZQUNUO1FBQ0o7UUFFQSxJQUFJeEIsZ0JBQWdCMkIsTUFBTSxHQUFHLEdBQUc7WUFDNUJMLE1BQU0sQ0FBQyxtQkFBbUIsR0FBRztnQkFDekJNLElBQUk1QixnQkFBZ0I2QixHQUFHLENBQUMsQ0FBQzFELE9BQVNBLEtBQUsyRCxLQUFLO1lBQ2hEO1FBQ0o7UUFFQSxJQUFJNUIsYUFBYSxNQUFNO1lBQ25Cb0IsTUFBTSxDQUFDLHdCQUF3QixHQUFHO2dCQUM5QlMsSUFBSTdCLFNBQVM0QixLQUFLO1lBQ3RCO1FBQ0o7UUFFQSxJQUFJdEUsV0FBVyxNQUFNO1lBQ2pCOEQsTUFBTSxDQUFDLFNBQVMsR0FBRztnQkFDZlMsSUFBSXZFLE9BQU9zRSxLQUFLO1lBQ3BCO1FBQ0o7UUFFQSxJQUFJekIsU0FBUyxNQUFNO1lBQ2ZpQixNQUFNLENBQUMsZUFBZSxHQUFHO2dCQUNyQlMsSUFBSTFCLEtBQUt5QixLQUFLO1lBQ2xCO1FBQ0o7UUFFQW5CLGNBQWM7WUFDVnFCLFdBQVc7Z0JBQ1BWO1lBQ0o7UUFDSjtJQUNKO0lBRUEsTUFBTVcsY0FBYztRQUNoQixJQUFJQyxXQUFXUCxNQUFNLEtBQUssR0FBRztZQUN6QjtRQUNKO1FBRUEsTUFBTVEsT0FBT0QsV0FBV0wsR0FBRyxDQUFDLENBQUMxRDtnQkFHckJBLHFCQUNBQSxrQkFDQUEsa0JBQ0FBO1lBTEosT0FBTztnQkFDSEEsS0FBS0UsUUFBUTtnQkFDYkYsQ0FBQUEsc0JBQUFBLEtBQUtvQixhQUFhLGNBQWxCcEIsaUNBQUFBLHNCQUFzQjtnQkFDdEJBLENBQUFBLG1CQUFBQSxLQUFLcUIsVUFBVSxjQUFmckIsOEJBQUFBLG1CQUFtQjtnQkFDbkJBLENBQUFBLG1CQUFBQSxLQUFLTCxVQUFVLGNBQWZLLDhCQUFBQSxtQkFBbUI7Z0JBQ25CQSxDQUFBQSxlQUFBQSxLQUFLWCxNQUFNLGNBQVhXLDBCQUFBQSxlQUFlO2dCQUNmQSxLQUFLc0IsT0FBTyxHQUFHL0MsNENBQUtBLENBQUN5QixLQUFLc0IsT0FBTyxFQUFFQyxNQUFNLENBQUMsZ0JBQWdCO2dCQUMxRHJDLGNBQWNjLEtBQUtLLFNBQVM7YUFDL0I7UUFDTDtRQUVBLE1BQU00RCxVQUFlO1lBQ2pCO2dCQUNJO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0g7U0FDSjtRQUVEdkYsc0VBQWNBLENBQUM7WUFDWHNGO1lBQ0FDO1FBQ0o7SUFDSjtJQUVBLE1BQU1DLGNBQWM7UUFDaEIsSUFBSUgsV0FBV1AsTUFBTSxLQUFLLEdBQUc7WUFDekI7UUFDSjtRQUVBLE1BQU1XLGFBQXlCO1lBQzNCO2dCQUNJO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2dCQUNBO2FBQ0g7U0FDSjtRQUVESixXQUFXSyxPQUFPLENBQUMsQ0FBQ3BFO2dCQUdaQSxxQkFDQUEsa0JBQ0FBLGtCQUNBQTtZQUxKbUUsV0FBV0UsSUFBSSxDQUFDO2dCQUNackUsS0FBS0UsUUFBUTtnQkFDYkYsQ0FBQUEsc0JBQUFBLEtBQUtvQixhQUFhLGNBQWxCcEIsaUNBQUFBLHNCQUFzQjtnQkFDdEJBLENBQUFBLG1CQUFBQSxLQUFLcUIsVUFBVSxjQUFmckIsOEJBQUFBLG1CQUFtQjtnQkFDbkJBLENBQUFBLG1CQUFBQSxLQUFLTCxVQUFVLGNBQWZLLDhCQUFBQSxtQkFBbUI7Z0JBQ25CQSxDQUFBQSxlQUFBQSxLQUFLWCxNQUFNLGNBQVhXLDBCQUFBQSxlQUFlO2dCQUNmQSxLQUFLc0IsT0FBTyxHQUFHL0MsNENBQUtBLENBQUN5QixLQUFLc0IsT0FBTyxFQUFFQyxNQUFNLENBQUMsZ0JBQWdCO2dCQUMxRHJDLGNBQWNjLEtBQUtLLFNBQVM7YUFDL0I7UUFDTDtRQUVBNUIsaUVBQVNBLENBQUMwRjtJQUNkO0lBRUEsTUFBTUosYUFBYTdGLDhDQUFPQSxDQUFnQjtZQUNsQnlFO1FBQXBCLE1BQU0yQixjQUFjM0IsQ0FBQUEsNkNBQUFBLGlCQUFBQSwyQkFBQUEsS0FBTTRCLDhCQUE4QixDQUFDQyxLQUFLLGNBQTFDN0Isd0RBQUFBLDZDQUE4QyxFQUFFO1FBRXBFLElBQUkyQixZQUFZZCxNQUFNLEtBQUssR0FBRztZQUMxQixPQUFPLEVBQUU7UUFDYjtRQUVBLE1BQU1pQixjQUE2QixFQUFFO1FBRXJDSCxZQUFZRixPQUFPLENBQUMsQ0FBQ007WUFDakIsTUFBTWpGLGFBQTBCO2dCQUM1QlMsVUFBVXdFLFlBQVk5RSxJQUFJO2dCQUMxQnlCLFlBQVlxRCxZQUFZN0UsY0FBYyxDQUFDQyxLQUFLO2dCQUM1Q0gsWUFDSStFLFlBQVkvRSxVQUFVLENBQUNELEVBQUUsSUFBSSxJQUN2QmlGLFlBQ0EsR0FBdUNELE9BQXBDQSxZQUFZL0UsVUFBVSxDQUFDaUYsU0FBUyxFQUFDLEtBQWtDLE9BQS9CRixZQUFZL0UsVUFBVSxDQUFDa0YsT0FBTztnQkFDL0V6RCxlQUFlc0QsWUFBWTNFLFNBQVMsQ0FBQ0QsS0FBSztnQkFDMUN3QixTQUFTb0QsWUFBWWpFLE9BQU8sR0FDdEIsSUFBSTZCLEtBQUtvQyxZQUFZakUsT0FBTyxJQUM1QmtFO2dCQUNOdEYsUUFBUXFGLFlBQVlyRixNQUFNO2dCQUMxQmdCLFdBQVcvQiwrREFBYUEsQ0FBQ29HO1lBQzdCO1lBQ0FELFlBQVlKLElBQUksQ0FBQzVFO1FBQ3JCO1FBRUEsT0FBT2dGO0lBQ1gsR0FBRztRQUFDaEM7UUFBUUM7UUFBU0M7S0FBSztJQUUxQixxQkFDSTs7MEJBQ0ksOERBQUMzRCx1REFBVUE7Z0JBQ1BjLE9BQU07Z0JBQ05nRix1QkFDSSw4REFBQzdGLHFIQUE4QkE7b0JBQzNCOEYsZUFBZWI7b0JBQ2ZjLGVBQWVsQjs7Ozs7Ozs7Ozs7MEJBSzNCLDhEQUFDakYsZ0VBQVNBO2dCQUNOOEIsU0FBU0E7Z0JBQ1RnQyxNQUFNb0I7Z0JBQ05rQixXQUFXeEMsVUFBVUM7Z0JBQ3JCd0MsV0FBV3pEO2dCQUNYMEQsVUFBVW5DO2dCQUNWb0MsZUFBZWxDO2dCQUNmbUMsYUFBYTs7Ozs7Ozs7QUFJN0I7R0EvTndCMUQ7O1FBQ0xoRCxzREFBU0E7UUFVMkJOLHlEQUFZQTs7O0tBWDNDc0Q7QUFpT2pCLE1BQU0yRCxpQkFBaUIsQ0FBQ2pGO0lBQzNCLE9BQU8sR0FNSEEsT0FMQUEsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXaEIsTUFBTSxLQUNqQjtRQUFDO1FBQVE7UUFBVTtLQUFNLENBQUNDLFFBQVEsQ0FBQ2UsVUFBVWhCLE1BQU0sSUFDN0NnQixzQkFBQUEsZ0NBQUFBLFVBQVdkLElBQUksR0FDZixJQUtQYyxPQUhDQSxDQUFBQSxzQkFBQUEsZ0NBQUFBLFVBQVdoQixNQUFNLE1BQUssZUFBZWdCLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV2QsSUFBSSxNQUFLLGtCQUNuRGMsc0JBQUFBLGdDQUFBQSxVQUFXZCxJQUFJLEdBQ2YsSUFFTmMsT0FEREEsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXaEIsTUFBTSxNQUFLLGFBQWFnQixzQkFBQUEsZ0NBQUFBLFVBQVdkLElBQUksR0FBRyxJQUtwRGMsT0FKQUEsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXaEIsTUFBTSxNQUFLLGVBQWViLHFEQUFPQSxDQUFDNkIsc0JBQUFBLGdDQUFBQSxVQUFXZCxJQUFJLElBQ3REYyxzQkFBQUEsZ0NBQUFBLFVBQVdoQixNQUFNLEdBQ2pCLElBT1QsT0FMR2dCLENBQUFBLHNCQUFBQSxnQ0FBQUEsVUFBV2hCLE1BQU0sTUFBSyxlQUN0QixDQUFDYixxREFBT0EsQ0FBQzZCLHNCQUFBQSxnQ0FBQUEsVUFBV2QsSUFBSSxLQUN4QmMsQ0FBQUEsc0JBQUFBLGdDQUFBQSxVQUFXZCxJQUFJLE1BQUssa0JBQ2RjLHNCQUFBQSxnQ0FBQUEsVUFBV2QsSUFBSSxHQUNmO0FBRWQsRUFBQyIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9zcmMvYXBwL3VpL3JlcG9ydGluZy9tYWludGVuYW5jZS1zdGF0dXMtYWN0aXZpdHktcmVwb3J0LnRzeD9hYjg4Il0sInNvdXJjZXNDb250ZW50IjpbIid1c2UgY2xpZW50J1xyXG5cclxuaW1wb3J0IFJlYWN0LCB7IHVzZU1lbW8sIHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnXHJcbmltcG9ydCBFeHBvcnRCdXR0b24gZnJvbSAnLi9leHBvcnQtYnV0dG9uJ1xyXG5pbXBvcnQgeyBHRVRfTUFJTlRFTkFOQ0VfU1RBVFVTX0FDVElWSVRZX0VOVFJJRVMgfSBmcm9tICdAL2FwcC9saWIvZ3JhcGhRTC9xdWVyeS9yZXBvcnRpbmcvR0VUX01BSU5URU5BTkNFX1NUQVRVU19BQ1RJVklUWV9FTlRSSUVTJ1xyXG5pbXBvcnQgeyB1c2VMYXp5UXVlcnkgfSBmcm9tICdAYXBvbGxvL2NsaWVudCdcclxuaW1wb3J0IHsgaXNPdmVyRHVlVGFzayB9IGZyb20gJ0AvYXBwL2xpYi9hY3Rpb25zJ1xyXG5pbXBvcnQgZGF5anMgZnJvbSAnZGF5anMnXHJcbmltcG9ydCB7IGlzRW1wdHkgfSBmcm9tICdsb2Rhc2gnXHJcbmltcG9ydCB7IGV4cG9ydENzdiB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvY3N2SGVscGVyJ1xyXG5pbXBvcnQgeyBleHBvcnRQZGZUYWJsZSB9IGZyb20gJ0AvYXBwL2hlbHBlcnMvcGRmSGVscGVyJ1xyXG5pbXBvcnQgeyB1c2VSb3V0ZXIgfSBmcm9tICduZXh0L25hdmlnYXRpb24nXHJcbmltcG9ydCB7IGNyZWF0ZUNvbHVtbnMsIERhdGFUYWJsZSwgUm93U3RhdHVzIH0gZnJvbSAnQC9jb21wb25lbnRzL2ZpbHRlcmVkVGFibGUnXHJcbmltcG9ydCB7IERhdGFUYWJsZVNvcnRIZWFkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvZGF0YS10YWJsZS1zb3J0LWhlYWRlcidcclxuaW1wb3J0IHsgU3RhdHVzQmFkZ2UgfSBmcm9tICdAL2FwcC91aS9tYWludGVuYW5jZS9saXN0L2xpc3QnXHJcblxyXG5pbXBvcnQgeyBBcnJvd0xlZnQgfSBmcm9tICdsdWNpZGUtcmVhY3QnXHJcbmltcG9ydCB7IEJ1dHRvbiwgQ2FyZCwgQ2FyZENvbnRlbnQsIExpc3RIZWFkZXIgfSBmcm9tICdAL2NvbXBvbmVudHMvdWknXHJcbmltcG9ydCB7IE1haW50ZW5hbmNlUmVwb3J0RmlsdGVyQWN0aW9ucyB9IGZyb20gJ0AvY29tcG9uZW50cy9maWx0ZXIvY29tcG9uZW50cy9tYWludGVuYW5jZS1yZXBvcnQtYWN0aW9ucydcclxuXHJcbmludGVyZmFjZSBEYXRlUmFuZ2Uge1xyXG4gICAgc3RhcnREYXRlOiBEYXRlIHwgbnVsbFxyXG4gICAgZW5kRGF0ZTogRGF0ZSB8IG51bGxcclxufVxyXG5cclxuaW50ZXJmYWNlIElEcm9wZG93bkl0ZW0ge1xyXG4gICAgbGFiZWw6IHN0cmluZ1xyXG4gICAgdmFsdWU6IHN0cmluZ1xyXG59XHJcblxyXG50eXBlIEZpbHRlclR5cGUgPSAnZGF0ZVJhbmdlJyB8ICd2ZXNzZWxzJyB8ICdjYXRlZ29yeScgfCAnc3RhdHVzJyB8ICdtZW1iZXInXHJcbmludGVyZmFjZSBJRmlsdGVyIHtcclxuICAgIHR5cGU6IEZpbHRlclR5cGVcclxuICAgIGRhdGE6IGFueVxyXG59XHJcblxyXG5pbnRlcmZhY2UgSVJlcG9ydEl0ZW0ge1xyXG4gICAgdGFza05hbWU6IHN0cmluZ1xyXG4gICAgaW52ZW50b3J5TmFtZT86IHN0cmluZ1xyXG4gICAgdmVzc2VsTmFtZT86IHN0cmluZ1xyXG4gICAgYXNzaWduZWRUbz86IHN0cmluZ1xyXG4gICAgc3RhdHVzPzogc3RyaW5nXHJcbiAgICBkdWVEYXRlPzogRGF0ZVxyXG4gICAgZHVlU3RhdHVzOiBhbnlcclxufVxyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGV4dHJhY3Qgc3RhdHVzIHRleHQgdXNpbmcgdGhlIGV4YWN0IHNhbWUgbG9naWMgYXMgU3RhdHVzQmFkZ2VcclxuLy8gVGhpcyBlbnN1cmVzIGNvbnNpc3RlbmN5IGJldHdlZW4gdmlzdWFsIGRpc3BsYXkgYW5kIGV4cG9ydGVkIGRhdGFcclxuY29uc3QgZ2V0U3RhdHVzVGV4dCA9IChpc092ZXJEdWU6IGFueSk6IHN0cmluZyA9PiB7XHJcbiAgICBsZXQgc3RhdHVzVGV4dCA9ICcnXHJcbiAgICBpZiAoXHJcbiAgICAgICAgaXNPdmVyRHVlPy5zdGF0dXMgJiZcclxuICAgICAgICBbJ0hpZ2gnLCAnTWVkaXVtJywgJ0xvdyddLmluY2x1ZGVzKGlzT3ZlckR1ZS5zdGF0dXMpXHJcbiAgICApIHtcclxuICAgICAgICBzdGF0dXNUZXh0ID0gaXNPdmVyRHVlPy5kYXlzXHJcbiAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgIGlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgIGlzT3ZlckR1ZT8uZGF5cyA9PT0gJ1NhdmUgQXMgRHJhZnQnXHJcbiAgICApIHtcclxuICAgICAgICBzdGF0dXNUZXh0ID0gaXNPdmVyRHVlPy5kYXlzXHJcbiAgICB9IGVsc2UgaWYgKGlzT3ZlckR1ZT8uc3RhdHVzID09PSAnVXBjb21pbmcnKSB7XHJcbiAgICAgICAgc3RhdHVzVGV4dCA9IGlzT3ZlckR1ZT8uZGF5c1xyXG4gICAgfSBlbHNlIGlmIChpc092ZXJEdWU/LnN0YXR1cyA9PT0gJ0NvbXBsZXRlZCcgJiYgaXNFbXB0eShpc092ZXJEdWU/LmRheXMpKSB7XHJcbiAgICAgICAgc3RhdHVzVGV4dCA9IGlzT3ZlckR1ZT8uc3RhdHVzXHJcbiAgICB9IGVsc2UgaWYgKFxyXG4gICAgICAgIGlzT3ZlckR1ZT8uc3RhdHVzID09PSAnQ29tcGxldGVkJyAmJlxyXG4gICAgICAgICFpc0VtcHR5KGlzT3ZlckR1ZT8uZGF5cykgJiZcclxuICAgICAgICBpc092ZXJEdWU/LmRheXMgIT09ICdTYXZlIEFzIERyYWZ0J1xyXG4gICAgKSB7XHJcbiAgICAgICAgc3RhdHVzVGV4dCA9IGlzT3ZlckR1ZT8uZGF5c1xyXG4gICAgfVxyXG4gICAgcmV0dXJuIHN0YXR1c1RleHQgfHwgJydcclxufVxyXG5cclxuLy8gSGVscGVyIGZ1bmN0aW9uIHRvIGNyZWF0ZSBhIGNvbXBhdGlibGUgTWFpbnRlbmFuY2VDaGVjayBvYmplY3QgZm9yIFN0YXR1c0JhZGdlXHJcbmNvbnN0IGNyZWF0ZU1haW50ZW5hbmNlQ2hlY2tGb3JCYWRnZSA9IChyZXBvcnRJdGVtOiBJUmVwb3J0SXRlbSkgPT4ge1xyXG4gICAgcmV0dXJuIHtcclxuICAgICAgICBpZDogMCwgLy8gTm90IG5lZWRlZCBmb3IgZGlzcGxheVxyXG4gICAgICAgIGFzc2lnbmVkVG86IHsgaWQ6IDAsIG5hbWU6ICcnIH0sIC8vIE5vdCBuZWVkZWQgZm9yIGRpc3BsYXlcclxuICAgICAgICBiYXNpY0NvbXBvbmVudDogeyBpZDogMCwgdGl0bGU6IG51bGwgfSwgLy8gTm90IG5lZWRlZCBmb3IgZGlzcGxheVxyXG4gICAgICAgIGludmVudG9yeTogeyBpZDogMCwgaXRlbTogbnVsbCB9LCAvLyBOb3QgbmVlZGVkIGZvciBkaXNwbGF5XHJcbiAgICAgICAgc3RhdHVzOiByZXBvcnRJdGVtLnN0YXR1cyB8fCAnJyxcclxuICAgICAgICByZWN1cnJpbmdJRDogMCwgLy8gTm90IG5lZWRlZCBmb3IgZGlzcGxheVxyXG4gICAgICAgIG5hbWU6IHJlcG9ydEl0ZW0udGFza05hbWUsXHJcbiAgICAgICAgY3JlYXRlZDogJycsIC8vIE5vdCBuZWVkZWQgZm9yIGRpc3BsYXlcclxuICAgICAgICBzZXZlcml0eTogJycsIC8vIE5vdCBuZWVkZWQgZm9yIGRpc3BsYXlcclxuICAgICAgICBpc092ZXJEdWU6IHJlcG9ydEl0ZW0uZHVlU3RhdHVzLCAvLyBUaGlzIGlzIHRoZSBrZXkgcHJvcGVydHkgU3RhdHVzQmFkZ2UgbmVlZHNcclxuICAgICAgICBjb21tZW50czogbnVsbCwgLy8gTm90IG5lZWRlZCBmb3IgZGlzcGxheVxyXG4gICAgICAgIHdvcmtPcmRlck51bWJlcjogbnVsbCwgLy8gTm90IG5lZWRlZCBmb3IgZGlzcGxheVxyXG4gICAgICAgIHN0YXJ0RGF0ZTogJycsIC8vIE5vdCBuZWVkZWQgZm9yIGRpc3BsYXlcclxuICAgICAgICBleHBpcmVzOiBudWxsLCAvLyBOb3QgbmVlZGVkIGZvciBkaXNwbGF5XHJcbiAgICAgICAgbWFpbnRlbmFuY2VDYXRlZ29yeUlEOiAwLCAvLyBOb3QgbmVlZGVkIGZvciBkaXNwbGF5XHJcbiAgICB9XHJcbn1cclxuXHJcbi8vIENvbHVtbiBkZWZpbml0aW9ucyBmb3IgdGhlIERhdGFUYWJsZVxyXG5jb25zdCBjb2x1bW5zID0gY3JlYXRlQ29sdW1uczxJUmVwb3J0SXRlbT4oW1xyXG4gICAge1xyXG4gICAgICAgIGFjY2Vzc29yS2V5OiAndGFza05hbWUnLFxyXG4gICAgICAgIGhlYWRlcjogKHsgY29sdW1uIH06IHsgY29sdW1uOiBhbnkgfSkgPT4gKFxyXG4gICAgICAgICAgICA8RGF0YVRhYmxlU29ydEhlYWRlciBjb2x1bW49e2NvbHVtbn0gdGl0bGU9XCJUYXNrIE5hbWVcIiAvPlxyXG4gICAgICAgICksXHJcbiAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBpdGVtID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgIHJldHVybiA8ZGl2IGNsYXNzTmFtZT1cImZvbnQtbWVkaXVtXCI+e2l0ZW0udGFza05hbWV9PC9kaXY+XHJcbiAgICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgYWNjZXNzb3JLZXk6ICdpbnZlbnRvcnlOYW1lJyxcclxuICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiSW52ZW50b3J5XCIgLz5cclxuICAgICAgICApLFxyXG4gICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgaXRlbSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICByZXR1cm4gPGRpdj57aXRlbS5pbnZlbnRvcnlOYW1lIHx8ICcnfTwvZGl2PlxyXG4gICAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGFjY2Vzc29yS2V5OiAndmVzc2VsTmFtZScsXHJcbiAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkxvY2F0aW9uXCIgLz5cclxuICAgICAgICApLFxyXG4gICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgaXRlbSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICByZXR1cm4gPGRpdj57aXRlbS52ZXNzZWxOYW1lIHx8ICcnfTwvZGl2PlxyXG4gICAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGFjY2Vzc29yS2V5OiAnYXNzaWduZWRUbycsXHJcbiAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkFzc2lnbmVkIFRvXCIgLz5cclxuICAgICAgICApLFxyXG4gICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgaXRlbSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICByZXR1cm4gPGRpdj57aXRlbS5hc3NpZ25lZFRvIHx8ICcnfTwvZGl2PlxyXG4gICAgICAgIH0sXHJcbiAgICB9LFxyXG4gICAge1xyXG4gICAgICAgIGFjY2Vzc29yS2V5OiAnc3RhdHVzJyxcclxuICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiU3RhdHVzXCIgLz5cclxuICAgICAgICApLFxyXG4gICAgICAgIGNlbGw6ICh7IHJvdyB9OiB7IHJvdzogYW55IH0pID0+IHtcclxuICAgICAgICAgICAgY29uc3QgaXRlbSA9IHJvdy5vcmlnaW5hbFxyXG4gICAgICAgICAgICByZXR1cm4gPGRpdj57aXRlbS5zdGF0dXMgfHwgJyd9PC9kaXY+XHJcbiAgICAgICAgfSxcclxuICAgIH0sXHJcbiAgICB7XHJcbiAgICAgICAgYWNjZXNzb3JLZXk6ICdkdWVEYXRlJyxcclxuICAgICAgICBoZWFkZXI6ICh7IGNvbHVtbiB9OiB7IGNvbHVtbjogYW55IH0pID0+IChcclxuICAgICAgICAgICAgPERhdGFUYWJsZVNvcnRIZWFkZXIgY29sdW1uPXtjb2x1bW59IHRpdGxlPVwiRHVlIERhdGVcIiAvPlxyXG4gICAgICAgICksXHJcbiAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBpdGVtID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgIHJldHVybiAoXHJcbiAgICAgICAgICAgICAgICA8ZGl2PlxyXG4gICAgICAgICAgICAgICAgICAgIHtpdGVtLmR1ZURhdGUgPyBkYXlqcyhpdGVtLmR1ZURhdGUpLmZvcm1hdCgnREQvTU0vWVknKSA6ICcnfVxyXG4gICAgICAgICAgICAgICAgPC9kaXY+XHJcbiAgICAgICAgICAgIClcclxuICAgICAgICB9LFxyXG4gICAgfSxcclxuICAgIHtcclxuICAgICAgICBhY2Nlc3NvcktleTogJ2R1ZVN0YXR1cycsXHJcbiAgICAgICAgaGVhZGVyOiAoeyBjb2x1bW4gfTogeyBjb2x1bW46IGFueSB9KSA9PiAoXHJcbiAgICAgICAgICAgIDxEYXRhVGFibGVTb3J0SGVhZGVyIGNvbHVtbj17Y29sdW1ufSB0aXRsZT1cIkR1ZSBTdGF0dXNcIiAvPlxyXG4gICAgICAgICksXHJcbiAgICAgICAgY2VsbDogKHsgcm93IH06IHsgcm93OiBhbnkgfSkgPT4ge1xyXG4gICAgICAgICAgICBjb25zdCBpdGVtID0gcm93Lm9yaWdpbmFsXHJcbiAgICAgICAgICAgIGNvbnN0IG1haW50ZW5hbmNlQ2hlY2sgPSBjcmVhdGVNYWludGVuYW5jZUNoZWNrRm9yQmFkZ2UoaXRlbSlcclxuICAgICAgICAgICAgcmV0dXJuIDxTdGF0dXNCYWRnZSBtYWludGVuYW5jZUNoZWNrPXttYWludGVuYW5jZUNoZWNrfSAvPlxyXG4gICAgICAgIH0sXHJcbiAgICB9LFxyXG5dKVxyXG5cclxuLy8gRnVuY3Rpb24gdG8gZXZhbHVhdGUgcm93IHN0YXR1cyBmb3IgaGlnaGxpZ2h0aW5nXHJcbmNvbnN0IGdldFJvd1N0YXR1cyA9IChyb3dEYXRhOiBJUmVwb3J0SXRlbSk6IFJvd1N0YXR1cyA9PiB7XHJcbiAgICBpZiAocm93RGF0YS5kdWVTdGF0dXM/LnN0YXR1cyA9PT0gJ0hpZ2gnKSB7XHJcbiAgICAgICAgcmV0dXJuICdvdmVyZHVlJ1xyXG4gICAgfVxyXG4gICAgaWYgKFxyXG4gICAgICAgIHJvd0RhdGEuZHVlU3RhdHVzPy5zdGF0dXMgPT09ICdNZWRpdW0nIHx8XHJcbiAgICAgICAgcm93RGF0YS5kdWVTdGF0dXM/LnN0YXR1cyA9PT0gJ0xvdydcclxuICAgICkge1xyXG4gICAgICAgIHJldHVybiAndXBjb21pbmcnXHJcbiAgICB9XHJcbiAgICByZXR1cm4gJ25vcm1hbCdcclxufVxyXG5cclxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gTWFpbnRlbmFuY2VTdGF0dXNBY3Rpdml0eVJlcG9ydCgpIHtcclxuICAgIGNvbnN0IHJvdXRlciA9IHVzZVJvdXRlcigpXHJcbiAgICBjb25zdCBbc2VsZWN0ZWRWZXNzZWxzLCBzZXRTZWxlY3RlZFZlc3NlbHNdID0gdXNlU3RhdGU8SURyb3Bkb3duSXRlbVtdPihbXSlcclxuICAgIGNvbnN0IFtjYXRlZ29yeSwgc2V0Q2F0ZWdvcnldID0gdXNlU3RhdGU8SURyb3Bkb3duSXRlbSB8IG51bGw+KG51bGwpXHJcbiAgICBjb25zdCBbc3RhdHVzLCBzZXRTdGF0dXNdID0gdXNlU3RhdGU8SURyb3Bkb3duSXRlbSB8IG51bGw+KG51bGwpXHJcbiAgICBjb25zdCBbY3Jldywgc2V0Q3Jld10gPSB1c2VTdGF0ZTxJRHJvcGRvd25JdGVtIHwgbnVsbD4obnVsbClcclxuICAgIGNvbnN0IFtkYXRlUmFuZ2UsIHNldERhdGVSYW5nZV0gPSB1c2VTdGF0ZTxEYXRlUmFuZ2U+KHtcclxuICAgICAgICBzdGFydERhdGU6IG5ldyBEYXRlKCksXHJcbiAgICAgICAgZW5kRGF0ZTogbmV3IERhdGUoKSxcclxuICAgIH0pXHJcblxyXG4gICAgY29uc3QgW2dldFJlcG9ydERhdGEsIHsgY2FsbGVkLCBsb2FkaW5nLCBkYXRhIH1dID0gdXNlTGF6eVF1ZXJ5KFxyXG4gICAgICAgIEdFVF9NQUlOVEVOQU5DRV9TVEFUVVNfQUNUSVZJVFlfRU5UUklFUyxcclxuICAgICAgICB7XHJcbiAgICAgICAgICAgIGZldGNoUG9saWN5OiAnY2FjaGUtYW5kLW5ldHdvcmsnLFxyXG4gICAgICAgICAgICBvbkVycm9yOiAoZXJyb3I6IGFueSkgPT4ge1xyXG4gICAgICAgICAgICAgICAgY29uc29sZS5lcnJvcihcclxuICAgICAgICAgICAgICAgICAgICAnR0VUX01BSU5URU5BTkNFX1NUQVRVU19BQ1RJVklUWV9FTlRSSUVTIGVycm9yJyxcclxuICAgICAgICAgICAgICAgICAgICBlcnJvcixcclxuICAgICAgICAgICAgICAgIClcclxuICAgICAgICAgICAgfSxcclxuICAgICAgICB9LFxyXG4gICAgKVxyXG5cclxuICAgIGNvbnN0IGhhbmRsZUZpbHRlck9uQ2hhbmdlID0gKHsgdHlwZSwgZGF0YSB9OiBJRmlsdGVyKSA9PiB7XHJcbiAgICAgICAgc3dpdGNoICh0eXBlKSB7XHJcbiAgICAgICAgICAgIGNhc2UgJ3Zlc3NlbHMnOlxyXG4gICAgICAgICAgICAgICAgc2V0U2VsZWN0ZWRWZXNzZWxzKGRhdGEpXHJcbiAgICAgICAgICAgICAgICBicmVha1xyXG4gICAgICAgICAgICBjYXNlICdjYXRlZ29yeSc6XHJcbiAgICAgICAgICAgICAgICBzZXRDYXRlZ29yeShkYXRhKVxyXG4gICAgICAgICAgICAgICAgYnJlYWtcclxuXHJcbiAgICAgICAgICAgIGNhc2UgJ3N0YXR1cyc6XHJcbiAgICAgICAgICAgICAgICBzZXRTdGF0dXMoZGF0YSlcclxuICAgICAgICAgICAgICAgIGJyZWFrXHJcblxyXG4gICAgICAgICAgICBjYXNlICdkYXRlUmFuZ2UnOlxyXG4gICAgICAgICAgICAgICAgc2V0RGF0ZVJhbmdlKGRhdGEgfHwgeyBzdGFydERhdGU6IG51bGwsIGVuZERhdGU6IG51bGwgfSlcclxuICAgICAgICAgICAgICAgIGJyZWFrXHJcblxyXG4gICAgICAgICAgICBjYXNlICdtZW1iZXInOlxyXG4gICAgICAgICAgICAgICAgc2V0Q3JldyhkYXRhKVxyXG4gICAgICAgICAgICAgICAgYnJlYWtcclxuXHJcbiAgICAgICAgICAgIGRlZmF1bHQ6XHJcbiAgICAgICAgICAgICAgICBicmVha1xyXG4gICAgICAgIH1cclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBnZW5lcmF0ZVJlcG9ydCA9ICgpID0+IHtcclxuICAgICAgICBjb25zdCBmaWx0ZXI6IGFueSA9IHt9XHJcblxyXG4gICAgICAgIGlmIChcclxuICAgICAgICAgICAgZGF0ZVJhbmdlICYmXHJcbiAgICAgICAgICAgIGRhdGVSYW5nZS5zdGFydERhdGUgIT09IG51bGwgJiZcclxuICAgICAgICAgICAgZGF0ZVJhbmdlLmVuZERhdGUgIT09IG51bGxcclxuICAgICAgICApIHtcclxuICAgICAgICAgICAgLy8gRm9ybWF0IGRhdGVzIGFzIFlZWVktTU0tREQgc3RyaW5ncyBmb3IgR3JhcGhRTFxyXG4gICAgICAgICAgICBjb25zdCBzdGFydERhdGVGb3JtYXR0ZWQgPSBkYXlqcyhkYXRlUmFuZ2Uuc3RhcnREYXRlKS5mb3JtYXQoXHJcbiAgICAgICAgICAgICAgICAnWVlZWS1NTS1ERCcsXHJcbiAgICAgICAgICAgIClcclxuICAgICAgICAgICAgY29uc3QgZW5kRGF0ZUZvcm1hdHRlZCA9IGRheWpzKGRhdGVSYW5nZS5lbmREYXRlKS5mb3JtYXQoXHJcbiAgICAgICAgICAgICAgICAnWVlZWS1NTS1ERCcsXHJcbiAgICAgICAgICAgIClcclxuXHJcbiAgICAgICAgICAgIGZpbHRlclsnZXhwaXJlcyddID0ge1xyXG4gICAgICAgICAgICAgICAgZ3RlOiBzdGFydERhdGVGb3JtYXR0ZWQsXHJcbiAgICAgICAgICAgICAgICBsdGU6IGVuZERhdGVGb3JtYXR0ZWQsXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChzZWxlY3RlZFZlc3NlbHMubGVuZ3RoID4gMCkge1xyXG4gICAgICAgICAgICBmaWx0ZXJbJ2Jhc2ljQ29tcG9uZW50SUQnXSA9IHtcclxuICAgICAgICAgICAgICAgIGluOiBzZWxlY3RlZFZlc3NlbHMubWFwKChpdGVtKSA9PiBpdGVtLnZhbHVlKSxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgaWYgKGNhdGVnb3J5ICE9PSBudWxsKSB7XHJcbiAgICAgICAgICAgIGZpbHRlclsnbWFpbnRlbmFuY2VDYXRlZ29yeUlEJ10gPSB7XHJcbiAgICAgICAgICAgICAgICBlcTogY2F0ZWdvcnkudmFsdWUsXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChzdGF0dXMgIT09IG51bGwpIHtcclxuICAgICAgICAgICAgZmlsdGVyWydzdGF0dXMnXSA9IHtcclxuICAgICAgICAgICAgICAgIGVxOiBzdGF0dXMudmFsdWUsXHJcbiAgICAgICAgICAgIH1cclxuICAgICAgICB9XHJcblxyXG4gICAgICAgIGlmIChjcmV3ICE9PSBudWxsKSB7XHJcbiAgICAgICAgICAgIGZpbHRlclsnYXNzaWduZWRUb0lEJ10gPSB7XHJcbiAgICAgICAgICAgICAgICBlcTogY3Jldy52YWx1ZSxcclxuICAgICAgICAgICAgfVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgZ2V0UmVwb3J0RGF0YSh7XHJcbiAgICAgICAgICAgIHZhcmlhYmxlczoge1xyXG4gICAgICAgICAgICAgICAgZmlsdGVyLFxyXG4gICAgICAgICAgICB9LFxyXG4gICAgICAgIH0pXHJcbiAgICB9XHJcblxyXG4gICAgY29uc3QgZG93bmxvYWRQZGYgPSAoKSA9PiB7XHJcbiAgICAgICAgaWYgKHJlcG9ydERhdGEubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgIHJldHVyblxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgYm9keSA9IHJlcG9ydERhdGEubWFwKChpdGVtOiBhbnkpID0+IHtcclxuICAgICAgICAgICAgcmV0dXJuIFtcclxuICAgICAgICAgICAgICAgIGl0ZW0udGFza05hbWUsXHJcbiAgICAgICAgICAgICAgICBpdGVtLmludmVudG9yeU5hbWUgPz8gJycsXHJcbiAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbE5hbWUgPz8gJycsXHJcbiAgICAgICAgICAgICAgICBpdGVtLmFzc2lnbmVkVG8gPz8gJycsXHJcbiAgICAgICAgICAgICAgICBpdGVtLnN0YXR1cyA/PyAnJyxcclxuICAgICAgICAgICAgICAgIGl0ZW0uZHVlRGF0ZSA/IGRheWpzKGl0ZW0uZHVlRGF0ZSkuZm9ybWF0KCdERC9NTS9ZWVlZJykgOiAnJyxcclxuICAgICAgICAgICAgICAgIGdldFN0YXR1c1RleHQoaXRlbS5kdWVTdGF0dXMpLFxyXG4gICAgICAgICAgICBdXHJcbiAgICAgICAgfSlcclxuXHJcbiAgICAgICAgY29uc3QgaGVhZGVyczogYW55ID0gW1xyXG4gICAgICAgICAgICBbXHJcbiAgICAgICAgICAgICAgICAnVGFzayBOYW1lJyxcclxuICAgICAgICAgICAgICAgICdJbnZlbnRvcnknLFxyXG4gICAgICAgICAgICAgICAgJ0xvY2F0aW9uJyxcclxuICAgICAgICAgICAgICAgICdBc3NpZ25lZCBUbycsXHJcbiAgICAgICAgICAgICAgICAnU3RhdHVzJyxcclxuICAgICAgICAgICAgICAgICdEdWUgRGF0ZScsXHJcbiAgICAgICAgICAgICAgICAnRHVlIFN0YXR1cycsXHJcbiAgICAgICAgICAgIF0sXHJcbiAgICAgICAgXVxyXG5cclxuICAgICAgICBleHBvcnRQZGZUYWJsZSh7XHJcbiAgICAgICAgICAgIGJvZHksXHJcbiAgICAgICAgICAgIGhlYWRlcnMsXHJcbiAgICAgICAgfSlcclxuICAgIH1cclxuXHJcbiAgICBjb25zdCBkb3dubG9hZENzdiA9ICgpID0+IHtcclxuICAgICAgICBpZiAocmVwb3J0RGF0YS5sZW5ndGggPT09IDApIHtcclxuICAgICAgICAgICAgcmV0dXJuXHJcbiAgICAgICAgfVxyXG5cclxuICAgICAgICBjb25zdCBjc3ZFbnRyaWVzOiBzdHJpbmdbXVtdID0gW1xyXG4gICAgICAgICAgICBbXHJcbiAgICAgICAgICAgICAgICAndGFzayBuYW1lJyxcclxuICAgICAgICAgICAgICAgICdpbnZlbnRvcnknLFxyXG4gICAgICAgICAgICAgICAgJ2xvY2F0aW9uJyxcclxuICAgICAgICAgICAgICAgICdhc3NpZ25lZCB0bycsXHJcbiAgICAgICAgICAgICAgICAnc3RhdHVzJyxcclxuICAgICAgICAgICAgICAgICdkdWUgZGF0ZScsXHJcbiAgICAgICAgICAgICAgICAnZHVlIHN0YXR1cycsXHJcbiAgICAgICAgICAgIF0sXHJcbiAgICAgICAgXVxyXG5cclxuICAgICAgICByZXBvcnREYXRhLmZvckVhY2goKGl0ZW0pID0+IHtcclxuICAgICAgICAgICAgY3N2RW50cmllcy5wdXNoKFtcclxuICAgICAgICAgICAgICAgIGl0ZW0udGFza05hbWUsXHJcbiAgICAgICAgICAgICAgICBpdGVtLmludmVudG9yeU5hbWUgPz8gJ04vQScsXHJcbiAgICAgICAgICAgICAgICBpdGVtLnZlc3NlbE5hbWUgPz8gJ04vQScsXHJcbiAgICAgICAgICAgICAgICBpdGVtLmFzc2lnbmVkVG8gPz8gJ04vQScsXHJcbiAgICAgICAgICAgICAgICBpdGVtLnN0YXR1cyA/PyAnTi9BJyxcclxuICAgICAgICAgICAgICAgIGl0ZW0uZHVlRGF0ZSA/IGRheWpzKGl0ZW0uZHVlRGF0ZSkuZm9ybWF0KCdERC9NTS9ZWVlZJykgOiAnTi9BJyxcclxuICAgICAgICAgICAgICAgIGdldFN0YXR1c1RleHQoaXRlbS5kdWVTdGF0dXMpLFxyXG4gICAgICAgICAgICBdKVxyXG4gICAgICAgIH0pXHJcblxyXG4gICAgICAgIGV4cG9ydENzdihjc3ZFbnRyaWVzKVxyXG4gICAgfVxyXG5cclxuICAgIGNvbnN0IHJlcG9ydERhdGEgPSB1c2VNZW1vPElSZXBvcnRJdGVtW10+KCgpID0+IHtcclxuICAgICAgICBjb25zdCBmZXRjaGVkRGF0YSA9IGRhdGE/LnJlYWRDb21wb25lbnRNYWludGVuYW5jZUNoZWNrcy5ub2RlcyA/PyBbXVxyXG5cclxuICAgICAgICBpZiAoZmV0Y2hlZERhdGEubGVuZ3RoID09PSAwKSB7XHJcbiAgICAgICAgICAgIHJldHVybiBbXVxyXG4gICAgICAgIH1cclxuXHJcbiAgICAgICAgY29uc3QgcmVwb3J0SXRlbXM6IElSZXBvcnRJdGVtW10gPSBbXVxyXG5cclxuICAgICAgICBmZXRjaGVkRGF0YS5mb3JFYWNoKChmZXRjaGVkSXRlbTogYW55KSA9PiB7XHJcbiAgICAgICAgICAgIGNvbnN0IHJlcG9ydEl0ZW06IElSZXBvcnRJdGVtID0ge1xyXG4gICAgICAgICAgICAgICAgdGFza05hbWU6IGZldGNoZWRJdGVtLm5hbWUsXHJcbiAgICAgICAgICAgICAgICB2ZXNzZWxOYW1lOiBmZXRjaGVkSXRlbS5iYXNpY0NvbXBvbmVudC50aXRsZSxcclxuICAgICAgICAgICAgICAgIGFzc2lnbmVkVG86XHJcbiAgICAgICAgICAgICAgICAgICAgZmV0Y2hlZEl0ZW0uYXNzaWduZWRUby5pZCA9PSAwXHJcbiAgICAgICAgICAgICAgICAgICAgICAgID8gdW5kZWZpbmVkXHJcbiAgICAgICAgICAgICAgICAgICAgICAgIDogYCR7ZmV0Y2hlZEl0ZW0uYXNzaWduZWRUby5maXJzdE5hbWV9ICR7ZmV0Y2hlZEl0ZW0uYXNzaWduZWRUby5zdXJuYW1lfWAsXHJcbiAgICAgICAgICAgICAgICBpbnZlbnRvcnlOYW1lOiBmZXRjaGVkSXRlbS5pbnZlbnRvcnkudGl0bGUsXHJcbiAgICAgICAgICAgICAgICBkdWVEYXRlOiBmZXRjaGVkSXRlbS5leHBpcmVzXHJcbiAgICAgICAgICAgICAgICAgICAgPyBuZXcgRGF0ZShmZXRjaGVkSXRlbS5leHBpcmVzKVxyXG4gICAgICAgICAgICAgICAgICAgIDogdW5kZWZpbmVkLFxyXG4gICAgICAgICAgICAgICAgc3RhdHVzOiBmZXRjaGVkSXRlbS5zdGF0dXMsXHJcbiAgICAgICAgICAgICAgICBkdWVTdGF0dXM6IGlzT3ZlckR1ZVRhc2soZmV0Y2hlZEl0ZW0pLFxyXG4gICAgICAgICAgICB9XHJcbiAgICAgICAgICAgIHJlcG9ydEl0ZW1zLnB1c2gocmVwb3J0SXRlbSlcclxuICAgICAgICB9KVxyXG5cclxuICAgICAgICByZXR1cm4gcmVwb3J0SXRlbXNcclxuICAgIH0sIFtjYWxsZWQsIGxvYWRpbmcsIGRhdGFdKVxyXG5cclxuICAgIHJldHVybiAoXHJcbiAgICAgICAgPD5cclxuICAgICAgICAgICAgPExpc3RIZWFkZXJcclxuICAgICAgICAgICAgICAgIHRpdGxlPVwiTWFpbnRlbmFuY2Ugc3RhdHVzIGFuZCBhY3Rpdml0eSByZXBvcnRcIlxyXG4gICAgICAgICAgICAgICAgYWN0aW9ucz17XHJcbiAgICAgICAgICAgICAgICAgICAgPE1haW50ZW5hbmNlUmVwb3J0RmlsdGVyQWN0aW9uc1xyXG4gICAgICAgICAgICAgICAgICAgICAgICBvbkRvd25sb2FkQ3N2PXtkb3dubG9hZENzdn1cclxuICAgICAgICAgICAgICAgICAgICAgICAgb25Eb3dubG9hZFBkZj17ZG93bmxvYWRQZGZ9XHJcbiAgICAgICAgICAgICAgICAgICAgLz5cclxuICAgICAgICAgICAgICAgIH1cclxuICAgICAgICAgICAgLz5cclxuXHJcbiAgICAgICAgICAgIDxEYXRhVGFibGVcclxuICAgICAgICAgICAgICAgIGNvbHVtbnM9e2NvbHVtbnN9XHJcbiAgICAgICAgICAgICAgICBkYXRhPXtyZXBvcnREYXRhfVxyXG4gICAgICAgICAgICAgICAgaXNMb2FkaW5nPXtjYWxsZWQgJiYgbG9hZGluZ31cclxuICAgICAgICAgICAgICAgIHJvd1N0YXR1cz17Z2V0Um93U3RhdHVzfVxyXG4gICAgICAgICAgICAgICAgb25DaGFuZ2U9e2hhbmRsZUZpbHRlck9uQ2hhbmdlfVxyXG4gICAgICAgICAgICAgICAgb25GaWx0ZXJDbGljaz17Z2VuZXJhdGVSZXBvcnR9XHJcbiAgICAgICAgICAgICAgICBzaG93VG9vbGJhcj17dHJ1ZX1cclxuICAgICAgICAgICAgLz5cclxuICAgICAgICA8Lz5cclxuICAgIClcclxufVxyXG5cclxuZXhwb3J0IGNvbnN0IGR1ZVN0YXR1c0xhYmVsID0gKGR1ZVN0YXR1czogYW55KTogc3RyaW5nID0+IHtcclxuICAgIHJldHVybiBgJHtcclxuICAgICAgICBkdWVTdGF0dXM/LnN0YXR1cyAmJlxyXG4gICAgICAgIFsnSGlnaCcsICdNZWRpdW0nLCAnTG93J10uaW5jbHVkZXMoZHVlU3RhdHVzLnN0YXR1cylcclxuICAgICAgICAgICAgPyBkdWVTdGF0dXM/LmRheXNcclxuICAgICAgICAgICAgOiAnJ1xyXG4gICAgfSR7XHJcbiAgICAgICAgZHVlU3RhdHVzPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnICYmIGR1ZVN0YXR1cz8uZGF5cyA9PT0gJ1NhdmUgQXMgRHJhZnQnXHJcbiAgICAgICAgICAgID8gZHVlU3RhdHVzPy5kYXlzXHJcbiAgICAgICAgICAgIDogJydcclxuICAgIH0ke2R1ZVN0YXR1cz8uc3RhdHVzID09PSAnVXBjb21pbmcnID8gZHVlU3RhdHVzPy5kYXlzIDogJyd9JHtcclxuICAgICAgICBkdWVTdGF0dXM/LnN0YXR1cyA9PT0gJ0NvbXBsZXRlZCcgJiYgaXNFbXB0eShkdWVTdGF0dXM/LmRheXMpXHJcbiAgICAgICAgICAgID8gZHVlU3RhdHVzPy5zdGF0dXNcclxuICAgICAgICAgICAgOiAnJ1xyXG4gICAgfSR7XHJcbiAgICAgICAgZHVlU3RhdHVzPy5zdGF0dXMgPT09ICdDb21wbGV0ZWQnICYmXHJcbiAgICAgICAgIWlzRW1wdHkoZHVlU3RhdHVzPy5kYXlzKSAmJlxyXG4gICAgICAgIGR1ZVN0YXR1cz8uZGF5cyAhPT0gJ1NhdmUgQXMgRHJhZnQnXHJcbiAgICAgICAgICAgID8gZHVlU3RhdHVzPy5kYXlzXHJcbiAgICAgICAgICAgIDogJydcclxuICAgIH1gXHJcbn1cclxuIl0sIm5hbWVzIjpbIlJlYWN0IiwidXNlTWVtbyIsInVzZVN0YXRlIiwiR0VUX01BSU5URU5BTkNFX1NUQVRVU19BQ1RJVklUWV9FTlRSSUVTIiwidXNlTGF6eVF1ZXJ5IiwiaXNPdmVyRHVlVGFzayIsImRheWpzIiwiaXNFbXB0eSIsImV4cG9ydENzdiIsImV4cG9ydFBkZlRhYmxlIiwidXNlUm91dGVyIiwiY3JlYXRlQ29sdW1ucyIsIkRhdGFUYWJsZSIsIkRhdGFUYWJsZVNvcnRIZWFkZXIiLCJTdGF0dXNCYWRnZSIsIkxpc3RIZWFkZXIiLCJNYWludGVuYW5jZVJlcG9ydEZpbHRlckFjdGlvbnMiLCJnZXRTdGF0dXNUZXh0IiwiaXNPdmVyRHVlIiwic3RhdHVzVGV4dCIsInN0YXR1cyIsImluY2x1ZGVzIiwiZGF5cyIsImNyZWF0ZU1haW50ZW5hbmNlQ2hlY2tGb3JCYWRnZSIsInJlcG9ydEl0ZW0iLCJpZCIsImFzc2lnbmVkVG8iLCJuYW1lIiwiYmFzaWNDb21wb25lbnQiLCJ0aXRsZSIsImludmVudG9yeSIsIml0ZW0iLCJyZWN1cnJpbmdJRCIsInRhc2tOYW1lIiwiY3JlYXRlZCIsInNldmVyaXR5IiwiZHVlU3RhdHVzIiwiY29tbWVudHMiLCJ3b3JrT3JkZXJOdW1iZXIiLCJzdGFydERhdGUiLCJleHBpcmVzIiwibWFpbnRlbmFuY2VDYXRlZ29yeUlEIiwiY29sdW1ucyIsImFjY2Vzc29yS2V5IiwiaGVhZGVyIiwiY29sdW1uIiwiY2VsbCIsInJvdyIsIm9yaWdpbmFsIiwiZGl2IiwiY2xhc3NOYW1lIiwiaW52ZW50b3J5TmFtZSIsInZlc3NlbE5hbWUiLCJkdWVEYXRlIiwiZm9ybWF0IiwibWFpbnRlbmFuY2VDaGVjayIsImdldFJvd1N0YXR1cyIsInJvd0RhdGEiLCJNYWludGVuYW5jZVN0YXR1c0FjdGl2aXR5UmVwb3J0Iiwicm91dGVyIiwic2VsZWN0ZWRWZXNzZWxzIiwic2V0U2VsZWN0ZWRWZXNzZWxzIiwiY2F0ZWdvcnkiLCJzZXRDYXRlZ29yeSIsInNldFN0YXR1cyIsImNyZXciLCJzZXRDcmV3IiwiZGF0ZVJhbmdlIiwic2V0RGF0ZVJhbmdlIiwiRGF0ZSIsImVuZERhdGUiLCJnZXRSZXBvcnREYXRhIiwiY2FsbGVkIiwibG9hZGluZyIsImRhdGEiLCJmZXRjaFBvbGljeSIsIm9uRXJyb3IiLCJlcnJvciIsImNvbnNvbGUiLCJoYW5kbGVGaWx0ZXJPbkNoYW5nZSIsInR5cGUiLCJnZW5lcmF0ZVJlcG9ydCIsImZpbHRlciIsInN0YXJ0RGF0ZUZvcm1hdHRlZCIsImVuZERhdGVGb3JtYXR0ZWQiLCJndGUiLCJsdGUiLCJsZW5ndGgiLCJpbiIsIm1hcCIsInZhbHVlIiwiZXEiLCJ2YXJpYWJsZXMiLCJkb3dubG9hZFBkZiIsInJlcG9ydERhdGEiLCJib2R5IiwiaGVhZGVycyIsImRvd25sb2FkQ3N2IiwiY3N2RW50cmllcyIsImZvckVhY2giLCJwdXNoIiwiZmV0Y2hlZERhdGEiLCJyZWFkQ29tcG9uZW50TWFpbnRlbmFuY2VDaGVja3MiLCJub2RlcyIsInJlcG9ydEl0ZW1zIiwiZmV0Y2hlZEl0ZW0iLCJ1bmRlZmluZWQiLCJmaXJzdE5hbWUiLCJzdXJuYW1lIiwiYWN0aW9ucyIsIm9uRG93bmxvYWRDc3YiLCJvbkRvd25sb2FkUGRmIiwiaXNMb2FkaW5nIiwicm93U3RhdHVzIiwib25DaGFuZ2UiLCJvbkZpbHRlckNsaWNrIiwic2hvd1Rvb2xiYXIiLCJkdWVTdGF0dXNMYWJlbCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/ui/reporting/maintenance-status-activity-report.tsx\n"));

/***/ })

});