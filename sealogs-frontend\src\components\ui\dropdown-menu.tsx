'use client'

import * as React from 'react'
import * as DropdownMenuPrimitive from '@radix-ui/react-dropdown-menu'
import { Check, ChevronRight, Circle, ArrowLeft } from 'lucide-react'
import { cva, type VariantProps } from 'class-variance-authority'

import { cn } from '@/app/lib/utils'

const dropdownMenuItemVariants = cva(
    'relative leading-5 flex cursor-default select-none items-center rounded-md outline-none focus:outline-none active:outline-none focus:bg-accent focus:text-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&>svg]:size-6 [&>svg]:shrink-0 will-change-transform will-change-width will-change-padding transform-gpu transition-[width,padding] ease-out duration-600',
    {
        variants: {
            variant: {
                default: 'gap-2 px-2 py-1.5',
                backButton:
                    'gap-[8px] px-[26px] w-full h-11 cursor-pointer hover:bg-accent hover:px-[6px] hover:w-[233px] hover:border hover:border-border',
            },
            hoverEffect: {
                true: 'flex flex-row group m-0 py-0 gap-[11px] justify-start px-[18px] h-11 cursor-pointer focus:bg-accent w-[233px]',
                false: '',
            },
        },
        defaultVariants: {
            variant: 'default',
            hoverEffect: false,
        },
    },
)

const DropdownMenu = DropdownMenuPrimitive.Root

const DropdownMenuTrigger = DropdownMenuPrimitive.Trigger

const DropdownMenuGroup = DropdownMenuPrimitive.Group

const DropdownMenuPortal = DropdownMenuPrimitive.Portal

const DropdownMenuSub = DropdownMenuPrimitive.Sub

const DropdownMenuRadioGroup = DropdownMenuPrimitive.RadioGroup

const DropdownMenuSubTrigger = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.SubTrigger>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubTrigger> & {
        inset?: boolean
        hoverEffect?: boolean
    }
>(({ className, inset, hoverEffect = true, children, ...props }, ref) => (
    <DropdownMenuPrimitive.SubTrigger
        ref={ref}
        className={cn(
            'flex cursor-default gap-2 select-none items-center rounded-md px-2 py-1.5 outline-none data-[state=open]:bg-accent [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0',
            inset && 'pl-8',
            {
                'flex flex-row group relative m-0 py-0 gap-[11px] justify-start px-[18px] h-11 cursor-pointer w-[233px]':
                    hoverEffect,
            },
            className,
        )}
        {...props}>
        <span className="z-10">{children}</span>
        <ChevronRight className="ml-auto z-10" />
        {hoverEffect && (
            <div
                className={cn(
                    'absolute w-full inset-0 mx-auto',
                    'group-hover:bg-accent group-hover:px-[6px] rounded-md group-hover:w-[233px] group-hover:border group-hover:border-border',
                    'will-change-transform will-change-width will-change-padding transform-gpu',
                    'transition-[width,padding] ease-out duration-600',
                    'outline-none focus:outline-none active:outline-none',
                )}
            />
        )}
    </DropdownMenuPrimitive.SubTrigger>
))
DropdownMenuSubTrigger.displayName =
    DropdownMenuPrimitive.SubTrigger.displayName

const DropdownMenuSubContent = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.SubContent>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.SubContent>
>(({ className, ...props }, ref) => (
    <DropdownMenuPrimitive.SubContent
        ref={ref}
        className={cn(
            'z-50 min-w-fit overflow-hidden rounded-lg border border-border bg-card p-2 text-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
            className,
        )}
        {...props}
    />
))
DropdownMenuSubContent.displayName =
    DropdownMenuPrimitive.SubContent.displayName

const DropdownMenuContent = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.Content>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
    <DropdownMenuPrimitive.Portal>
        <DropdownMenuPrimitive.Content
            ref={ref}
            sideOffset={sideOffset}
            className={cn(
                'z-50 min-w-fit rounded-[6px] shadow-[0px_4px_6px_#00000083] overflow-hidden border border-border bg-card p-2 text-foreground ',
                'data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2',
                className,
            )}
            {...props}
        />
    </DropdownMenuPrimitive.Portal>
))
DropdownMenuContent.displayName = DropdownMenuPrimitive.Content.displayName

const DropdownMenuItem = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.Item>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Item> &
        VariantProps<typeof dropdownMenuItemVariants> & {
            inset?: boolean
            hoverEffect?: boolean
        }
>(({ className, inset, hoverEffect, variant, ...props }, ref) => {
    // Determine the effective hoverEffect value based on variant and explicit prop
    const effectiveHoverEffect =
        variant === 'backButton' ? false : hoverEffect ?? true

    return (
        <DropdownMenuPrimitive.Item
            ref={ref}
            className={cn(
                dropdownMenuItemVariants({
                    variant,
                    hoverEffect: effectiveHoverEffect,
                }),
                inset && 'pl-8',
                className,
            )}
            {...props}>
            {effectiveHoverEffect && (
                <div
                    className={cn(
                        'absolute scale-110 inset-0 pointer-events-none mx-auto',
                        'group-hover:bg-accent group-hover:px-[6px] -z-10 rounded-md group-hover:scale-100 group-hover:border group-hover:border-border',
                        'will-change-transform will-change-width will-change-padding transform-gpu',
                        'transition-[transform,padding] ease-out duration-600',
                        'outline-none focus:outline-none active:outline-none',
                    )}
                />
            )}
            {variant === 'backButton' && (
                <>
                    <ArrowLeft size={16} />
                    <span>{props.children}</span>
                </>
            )}
            {variant !== 'backButton' && props.children}
        </DropdownMenuPrimitive.Item>
    )
})
DropdownMenuItem.displayName = DropdownMenuPrimitive.Item.displayName

const DropdownMenuCheckboxItem = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.CheckboxItem>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.CheckboxItem>
>(({ className, children, checked, ...props }, ref) => (
    <DropdownMenuPrimitive.CheckboxItem
        ref={ref}
        className={cn(
            'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2  outline-none transition-colors focus:bg-accent focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
            className,
        )}
        checked={checked}
        {...props}>
        <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
            <DropdownMenuPrimitive.ItemIndicator>
                <Check className="h-4 w-4" />
            </DropdownMenuPrimitive.ItemIndicator>
        </span>
        {children}
    </DropdownMenuPrimitive.CheckboxItem>
))
DropdownMenuCheckboxItem.displayName =
    DropdownMenuPrimitive.CheckboxItem.displayName

const DropdownMenuRadioItem = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.RadioItem>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.RadioItem>
>(({ className, children, ...props }, ref) => (
    <DropdownMenuPrimitive.RadioItem
        ref={ref}
        className={cn(
            'relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2  outline-none transition-colors focus:bg-accent/90 focus:text-primary data-[disabled]:pointer-events-none data-[disabled]:opacity-50',
            className,
        )}
        {...props}>
        <span className="absolute left-2 flex h-3.5 w-3.5 items-center justify-center">
            <DropdownMenuPrimitive.ItemIndicator>
                <Circle className="h-2 w-2 fill-current" />
            </DropdownMenuPrimitive.ItemIndicator>
        </span>
        {children}
    </DropdownMenuPrimitive.RadioItem>
))
DropdownMenuRadioItem.displayName = DropdownMenuPrimitive.RadioItem.displayName

const DropdownMenuLabel = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.Label>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Label> & {
        inset?: boolean
    }
>(({ className, inset, ...props }, ref) => (
    <DropdownMenuPrimitive.Label
        ref={ref}
        className={cn('px-2 py-1.5  ', inset && 'pl-8', className)}
        {...props}
    />
))
DropdownMenuLabel.displayName = DropdownMenuPrimitive.Label.displayName

const DropdownMenuSeparator = React.forwardRef<
    React.ElementRef<typeof DropdownMenuPrimitive.Separator>,
    React.ComponentPropsWithoutRef<typeof DropdownMenuPrimitive.Separator>
>(({ className, ...props }, ref) => (
    <DropdownMenuPrimitive.Separator
        ref={ref}
        className={cn('-mx-1 my-1 h-px bg-border', className)}
        {...props}
    />
))
DropdownMenuSeparator.displayName = DropdownMenuPrimitive.Separator.displayName

const DropdownMenuShortcut = ({
    className,
    ...props
}: React.HTMLAttributes<HTMLSpanElement>) => {
    return (
        <span
            className={cn('ml-auto  tracking-widest opacity-60', className)}
            {...props}
        />
    )
}
DropdownMenuShortcut.displayName = 'DropdownMenuShortcut'

export {
    DropdownMenu,
    DropdownMenuTrigger,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuCheckboxItem,
    DropdownMenuRadioItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuShortcut,
    DropdownMenuGroup,
    DropdownMenuPortal,
    DropdownMenuSub,
    DropdownMenuSubContent,
    DropdownMenuSubTrigger,
    DropdownMenuRadioGroup,
}
