'use client'

import React, { useMemo, useState } from 'react'
import { GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES } from '@/app/lib/graphQL/query/reporting/GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES'
import { useLazyQuery } from '@apollo/client'
import { isOverDueTask } from '@/app/lib/actions'
import dayjs from 'dayjs'
import { isEmpty } from 'lodash'
import { exportCsv } from '@/app/helpers/csvHelper'
import { exportPdfTable } from '@/app/helpers/pdfHelper'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import { createColumns, DataTable, RowStatus } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { StatusBadge } from '@/app/ui/maintenance/list/list'
import { MaintenanceReportFilterActions } from '@/components/filter/components/maintenance-report-actions'
import { ListHeader } from '@/components/ui'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui'
import { cn } from '@/app/lib/utils'

interface DateRange {
    startDate: Date | null
    endDate: Date | null
}

interface IDropdownItem {
    label: string
    value: string
}

type FilterType = 'dateRange' | 'vessels' | 'category' | 'status' | 'member'
interface IFilter {
    type: FilterType
    data: any
}

interface IReportItem {
    taskName: string
    inventoryName?: string
    vesselName?: string
    assignedTo?: string
    status?: string
    dueDate?: Date
    dueStatus: any
}

// Helper functions for generating initials (similar to maintenance list)
const getCrewInitials = (assignedTo?: string): string => {
    if (!assignedTo) return '??'
    const names = assignedTo.trim().split(' ')
    if (names.length === 1) {
        return names[0].substring(0, 2).toUpperCase()
    }
    const first = names[0]?.charAt(0)?.toUpperCase() || ''
    const last = names[names.length - 1]?.charAt(0)?.toUpperCase() || ''
    return `${first}${last}` || '??'
}

const getVesselInitials = (vesselName?: string): string => {
    if (!vesselName) return '??'
    const words = vesselName.split(' ').filter((word) => word.length > 0)
    if (words.length === 1) {
        return words[0].substring(0, 2).toUpperCase()
    }
    return words
        .slice(0, 2)
        .map((word) => word.charAt(0).toUpperCase())
        .join('')
}

// Helper function to extract status text using the exact same logic as StatusBadge
// This ensures consistency between visual display and exported data
const getStatusText = (isOverDue: any): string => {
    let statusText = ''
    if (
        isOverDue?.status &&
        ['High', 'Medium', 'Low'].includes(isOverDue.status)
    ) {
        statusText = isOverDue?.days
    } else if (
        isOverDue?.status === 'Completed' &&
        isOverDue?.days === 'Save As Draft'
    ) {
        statusText = isOverDue?.days
    } else if (isOverDue?.status === 'Upcoming') {
        statusText = isOverDue?.days
    } else if (isOverDue?.status === 'Completed' && isEmpty(isOverDue?.days)) {
        statusText = isOverDue?.status
    } else if (
        isOverDue?.status === 'Completed' &&
        !isEmpty(isOverDue?.days) &&
        isOverDue?.days !== 'Save As Draft'
    ) {
        statusText = isOverDue?.days
    }
    return statusText || ''
}

// Helper function to create a compatible MaintenanceCheck object for StatusBadge
const createMaintenanceCheckForBadge = (reportItem: IReportItem) => {
    return {
        id: 0, // Not needed for display
        assignedTo: { id: 0, name: '' }, // Not needed for display
        basicComponent: { id: 0, title: null }, // Not needed for display
        inventory: { id: 0, item: null }, // Not needed for display
        status: reportItem.status || '',
        recurringID: 0, // Not needed for display
        name: reportItem.taskName,
        created: '', // Not needed for display
        severity: '', // Not needed for display
        isOverDue: reportItem.dueStatus, // This is the key property StatusBadge needs
        comments: null, // Not needed for display
        workOrderNumber: null, // Not needed for display
        startDate: '', // Not needed for display
        expires: null, // Not needed for display
        maintenanceCategoryID: 0, // Not needed for display
    }
}

// Column definitions for the DataTable
const columns = createColumns<IReportItem>([
    {
        accessorKey: 'title',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Task Name" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div className="space-y-2">
                    <div className="flex items-center gap-2">
                        <div className="font-medium">{item.taskName}</div>
                    </div>

                    {/* Mobile: Show location */}
                    <div className="lg:hidden">
                        {item.vesselName && (
                            <div className="text-sm text-outer-space-400">
                                <span className="text-outer-space-400">
                                    Location:{' '}
                                </span>
                                <span className="hover:text-curious-blue-400">
                                    {item.vesselName}
                                </span>
                            </div>
                        )}
                    </div>

                    {/* Mobile: Show assigned to and inventory */}
                    <div className="md:hidden space-y-1">
                        {item.assignedTo && (
                            <div className="text-sm">
                                <span className="text-outer-space-400">
                                    Assigned to:{' '}
                                </span>
                                <span className="hover:text-curious-blue-400">
                                    {item.assignedTo}
                                </span>
                            </div>
                        )}
                        {item.inventoryName && (
                            <div className="text-sm">
                                <span className="text-outer-space-400">
                                    Inventory:{' '}
                                </span>
                                <span className="hover:text-curious-blue-400">
                                    {item.inventoryName}
                                </span>
                            </div>
                        )}
                        <div>
                            <StatusBadge
                                maintenanceCheck={createMaintenanceCheckForBadge(
                                    item,
                                )}
                            />
                        </div>
                    </div>
                </div>
            )
        },
        sortingFn: (rowA: any, rowB: any) => {
            const valueA = rowA?.original?.taskName || ''
            const valueB = rowB?.original?.taskName || ''
            return valueA.localeCompare(valueB)
        },
    },
    {
        accessorKey: 'inventoryName',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Inventory" />
        ),
        cellAlignment: 'left' as const,
        breakpoint: 'tablet-lg',
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div className="hidden md:block">
                    {item.inventoryName ? (
                        <span className="hover:text-curious-blue-400">
                            {item.inventoryName}
                        </span>
                    ) : (
                        <span>-</span>
                    )}
                </div>
            )
        },
        sortingFn: (rowA: any, rowB: any) => {
            const valueA = rowA?.original?.inventoryName || ''
            const valueB = rowB?.original?.inventoryName || ''
            return valueA.localeCompare(valueB)
        },
    },
    {
        accessorKey: 'vesselName',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Location" />
        ),
        cellAlignment: 'left' as const,
        breakpoint: 'laptop',
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div className="hidden lg:block">
                    {item.vesselName && (
                        <div className="flex items-center gap-2.5">
                            <Avatar size="sm" variant="secondary">
                                <AvatarFallback className="text-xs">
                                    {getVesselInitials(item.vesselName)}
                                </AvatarFallback>
                            </Avatar>
                            <span className="hover:text-curious-blue-400">
                                {item.vesselName}
                            </span>
                        </div>
                    )}
                </div>
            )
        },
        sortingFn: (rowA: any, rowB: any) => {
            const valueA = rowA?.original?.vesselName || ''
            const valueB = rowB?.original?.vesselName || ''
            return valueA.localeCompare(valueB)
        },
    },
    {
        accessorKey: 'assignedTo',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Assigned To" />
        ),
        cellAlignment: 'left' as const,
        breakpoint: 'tablet-lg',
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div className="hidden md:block">
                    {item.assignedTo && (
                        <div className="flex items-center gap-2.5">
                            <Avatar className="h-8 w-8">
                                <AvatarFallback className="text-xs">
                                    {getCrewInitials(item.assignedTo)}
                                </AvatarFallback>
                            </Avatar>
                            <span className="hover:text-curious-blue-400 hidden lg:block">
                                {item.assignedTo}
                            </span>
                        </div>
                    )}
                </div>
            )
        },
        sortingFn: (rowA: any, rowB: any) => {
            const valueA = rowA?.original?.assignedTo || ''
            const valueB = rowB?.original?.assignedTo || ''
            return valueA.localeCompare(valueB)
        },
    },
    {
        accessorKey: 'status',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Status" />
        ),
        cellAlignment: 'left' as const,
        breakpoint: 'tablet-sm',
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div className="hidden tablet-sm:block">
                    {item.status || '-'}
                </div>
            )
        },
        sortingFn: (rowA: any, rowB: any) => {
            const valueA = rowA?.original?.status || ''
            const valueB = rowB?.original?.status || ''
            return valueA.localeCompare(valueB)
        },
    },
    {
        accessorKey: 'dueDate',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Due Date" />
        ),
        cellAlignment: 'left' as const,
        breakpoint: 'tablet-sm',
        cell: ({ row }: { row: any }) => {
            const item = row.original
            return (
                <div className="hidden tablet-sm:block">
                    {item.dueDate ? dayjs(item.dueDate).format('DD/MM/YY') : '-'}
                </div>
            )
        },
        sortingFn: (rowA: any, rowB: any) => {
            const valueA = rowA?.original?.dueDate || ''
            const valueB = rowB?.original?.dueDate || ''
            return dayjs(valueA).unix() - dayjs(valueB).unix()
        },
    },
    {
        accessorKey: 'dueStatus',
        header: ({ column }: { column: any }) => (
            <DataTableSortHeader column={column} title="Due Status" />
        ),
        cell: ({ row }: { row: any }) => {
            const item = row.original
            const maintenanceCheck = createMaintenanceCheckForBadge(item)
            return <StatusBadge maintenanceCheck={maintenanceCheck} />
        },
    },
])

// Function to evaluate row status for highlighting
const getRowStatus = (rowData: IReportItem): RowStatus => {
    if (rowData.dueStatus?.status === 'High') {
        return 'overdue'
    }
    if (
        rowData.dueStatus?.status === 'Medium' ||
        rowData.dueStatus?.status === 'Low'
    ) {
        return 'upcoming'
    }
    return 'normal'
}

export default function MaintenanceStatusActivityReport() {
    const router = useRouter()
    const [selectedVessels, setSelectedVessels] = useState<IDropdownItem[]>([])
    const [category, setCategory] = useState<IDropdownItem | null>(null)
    const [status, setStatus] = useState<IDropdownItem | null>(null)
    const [crew, setCrew] = useState<IDropdownItem | null>(null)
    const [dateRange, setDateRange] = useState<DateRange>({
        startDate: null,
        endDate: null,
    })

    const [getReportData, { called, loading, data }] = useLazyQuery(
        GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error(
                    'GET_MAINTENANCE_STATUS_ACTIVITY_ENTRIES error',
                    error,
                )
            },
        },
    )

    const handleFilterOnChange = ({ type, data }: IFilter) => {
        switch (type) {
            case 'vessels':
                setSelectedVessels(data)
                break
            case 'category':
                setCategory(data)
                break

            case 'status':
                setStatus(data)
                break

            case 'dateRange':
                setDateRange(data || { startDate: null, endDate: null })
                break

            case 'member':
                setCrew(data)
                break

            default:
                break
        }
    }

    const generateReport = () => {
        const filter: any = {}

        if (
            dateRange &&
            dateRange.startDate !== null &&
            dateRange.endDate !== null
        ) {
            // Format dates as YYYY-MM-DD strings for GraphQL
            const startDateFormatted = dayjs(dateRange.startDate).format(
                'YYYY-MM-DD',
            )
            const endDateFormatted = dayjs(dateRange.endDate).format(
                'YYYY-MM-DD',
            )

            filter['expires'] = {
                gte: startDateFormatted,
                lte: endDateFormatted,
            }
        }

        if (selectedVessels.length > 0) {
            filter['basicComponentID'] = {
                in: selectedVessels.map((item) => item.value),
            }
        }

        if (category !== null) {
            filter['maintenanceCategoryID'] = {
                eq: category.value,
            }
        }

        if (status !== null) {
            filter['status'] = {
                eq: status.value,
            }
        }

        if (crew !== null) {
            filter['assignedToID'] = {
                eq: crew.value,
            }
        }

        getReportData({
            variables: {
                filter,
            },
        })
    }

    const downloadPdf = () => {
        if (reportData.length === 0) {
            return
        }

        const body = reportData.map((item: any) => {
            return [
                item.taskName,
                item.inventoryName ?? '',
                item.vesselName ?? '',
                item.assignedTo ?? '',
                item.status ?? '',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : '',
                getStatusText(item.dueStatus),
            ]
        })

        const headers: any = [
            [
                'Task Name',
                'Inventory',
                'Location',
                'Assigned To',
                'Status',
                'Due Date',
                'Due Status',
            ],
        ]

        exportPdfTable({
            body,
            headers,
        })
    }

    const downloadCsv = () => {
        if (reportData.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'task name',
                'inventory',
                'location',
                'assigned to',
                'status',
                'due date',
                'due status',
            ],
        ]

        reportData.forEach((item) => {
            csvEntries.push([
                item.taskName,
                item.inventoryName ?? 'N/A',
                item.vesselName ?? 'N/A',
                item.assignedTo ?? 'N/A',
                item.status ?? 'N/A',
                item.dueDate ? dayjs(item.dueDate).format('DD/MM/YYYY') : 'N/A',
                getStatusText(item.dueStatus),
            ])
        })

        exportCsv(csvEntries)
    }

    const reportData = useMemo<IReportItem[]>(() => {
        const fetchedData = data?.readComponentMaintenanceChecks.nodes ?? []

        if (fetchedData.length === 0) {
            return []
        }

        const reportItems: IReportItem[] = []

        fetchedData.forEach((fetchedItem: any) => {
            const reportItem: IReportItem = {
                taskName: fetchedItem.name,
                vesselName: fetchedItem.basicComponent.title,
                assignedTo:
                    fetchedItem.assignedTo.id == 0
                        ? undefined
                        : `${fetchedItem.assignedTo.firstName} ${fetchedItem.assignedTo.surname}`,
                inventoryName: fetchedItem.inventory.title,
                dueDate: fetchedItem.expires
                    ? new Date(fetchedItem.expires)
                    : undefined,
                status: fetchedItem.status,
                dueStatus: isOverDueTask(fetchedItem),
            }
            reportItems.push(reportItem)
        })

        return reportItems
    }, [called, loading, data])

    return (
        <>
            <ListHeader
                title="Maintenance status and activity report"
                actions={
                    <MaintenanceReportFilterActions
                        onDownloadCsv={downloadCsv}
                        onDownloadPdf={downloadPdf}
                    />
                }
            />

            <DataTable
                columns={columns}
                data={reportData}
                isLoading={called && loading}
                rowStatus={getRowStatus}
                onChange={handleFilterOnChange}
                onFilterClick={generateReport}
                showToolbar={true}
            />
        </>
    )
}

export const dueStatusLabel = (dueStatus: any): string => {
    return `${
        dueStatus?.status &&
        ['High', 'Medium', 'Low'].includes(dueStatus.status)
            ? dueStatus?.days
            : ''
    }${
        dueStatus?.status === 'Completed' && dueStatus?.days === 'Save As Draft'
            ? dueStatus?.days
            : ''
    }${dueStatus?.status === 'Upcoming' ? dueStatus?.days : ''}${
        dueStatus?.status === 'Completed' && isEmpty(dueStatus?.days)
            ? dueStatus?.status
            : ''
    }${
        dueStatus?.status === 'Completed' &&
        !isEmpty(dueStatus?.days) &&
        dueStatus?.days !== 'Save As Draft'
            ? dueStatus?.days
            : ''
    }`
}
